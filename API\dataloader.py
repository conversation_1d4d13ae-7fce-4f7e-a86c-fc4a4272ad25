import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import glob
import random


##  us 数据集
class Gremlin(Dataset):
    def __init__(self, file_list, transform=None):
        self.transform = transform
        self.samples = file_list
        if not self.samples:
            # This allows an "empty" dataset if no files are provided
            self.xshape = None 
            self.tshape = None
            return # Important to return if samples is empty, to avoid error in _get_shape
        
        self.samples.sort()
        try:
            self.xshape, self.tshape = self._get_shape()
        except Exception as e: # General exception for issues in _get_shape
            first_sample = self.samples[0] # samples is guaranteed not empty here
            raise RuntimeError(f"Error in _get_shape() for first sample '{first_sample}': {e}. Check .npz content and Gremlin._get_shape assumptions.")

    def _get_shape(self):
        # UNCHANGED FROM YOUR ORIGINAL
        with np.load(self.samples[0]) as data:
            x_np = data['xdata']
            t_np = data['ydata']
            
            xshape = np.moveaxis(x_np, -1, 0).shape
            tshape = np.expand_dims(t_np, axis=0).shape
        return xshape, tshape

    def __len__(self):
        # UNCHANGED FROM YOUR ORIGINAL
        return len(self.samples)

    def __getitem__(self, idx):
        # UNCHANGED FROM YOUR ORIGINAL (except no .copy())
        f = self.samples[idx]
        try:
            with np.load(f) as data:
                x_np = data['xdata'][:,:,:3] # ASSUMED (H, W, C_in)  #为了配合 bcunet 和 vcbnet，使用预训练参数，而设置为3
                #x_np = data['xdata']
                #print("aaaaaaaaaaaaaaa",x_np.shape)
                t_np = data['ydata'] # ASSUMED (H, W)
        except Exception as e:
            raise IOError(f"Error loading or processing {f}: {e}")

        x_np_transformed = np.moveaxis(x_np, -1, 0)
        t_np_transformed = np.expand_dims(t_np, axis=0)

        x = torch.from_numpy(x_np_transformed).float()
        t = torch.from_numpy(t_np_transformed).float()

        if self.transform is not None:
            x = self.transform(x)
            t = self.transform(t)
        return x, t
# --- END OF MODIFIED Gremlin CLASS ---

##  该函数 应用在 us数据集上
def _collect_files_for_year(data_root, year):
    # (No changes, no prints)
    all_files = []
    year_path = os.path.join(data_root, str(year))
    if not os.path.isdir(year_path):
        return []
    found_in_year = False
    for month_num in range(1, 13):
        month_str = f"{month_num:02d}"
        month_path = os.path.join(year_path, month_str)
        if os.path.isdir(month_path):
            pattern = os.path.join(month_path, "*.npz")
            month_files = glob.glob(pattern)
            if month_files:
                all_files.extend(month_files)
                found_in_year = True
    if found_in_year:
        all_files.sort()
    return all_files

#### ------------------------CHINA--------------------------------------------
class RadarSets(Dataset):
    def __init__(self, path, img_size, mode='train', data_type='all'):
        super(RadarSets, self).__init__()
        self.path = path
        self.mode = mode
        self.height, self.width = img_size
        self.data_type = data_type

        # 检查 path 是文件还是目录
        if os.path.isdir(self.path):
            self.file_list = [f for f in os.listdir(self.path) if f.endswith('.npy')]
        else:
            self.file_list = [os.path.basename(self.path)]  # 单文件路径
            self.path = os.path.dirname(self.path)  # 获取文件所在目录


    def __getitem__(self, index):
        data_path = os.path.join(self.path, self.file_list[index])
        data = np.load(data_path)

        if self.data_type == 'ir':
            data_channel = [7, 12, 13, 15, 16]
        elif self.data_type == 'vis':
            data_channel = [2, 5, 6]
        elif self.data_type == 'all':
            data_channel = [2, 5, 6,7,12, 13, 15, 16]
        else:
            raise ValueError("请指定数据类型 'vis' 或 'ir'")

        inputs = np.zeros(shape=[len(data_channel),
                                 self.height, self.width], dtype='float32')
        for i in range(len(data_channel)):
            inputs[i,:,:] = data[data_channel[i],:,:]
        inputs = torch.from_numpy(inputs)
        targets = np.expand_dims(data[0], axis=0)  # h w -> c h w
        targets = torch.from_numpy(targets)
        return inputs, targets
    def __len__(self):
        return len(self.file_list)


###-------------------------------------加载函数---------------------------------------------
def load_data(batch_size, val_batch_size, num_workers, data_name, data_type, in_shape,
              **kwargs):
    # data_path = {"us": '/Storage01/ShareData/radar_us/STR_US/counts/',
    #             "ca": '/Storage01/ShareData/STR/'}
    random_seed=42
    random.seed(random_seed)
    ###  us 数据集部分
    if data_name == 'us':
        data_root = '/Storage01/ShareData/radar_us/STR_US/counts/'
        data_year=2020
        train_split_ratio=0.8
        all_files_for_year = _collect_files_for_year(data_root, data_year)
        if not all_files_for_year:
            # This FileNotFoundError is acceptable as it indicates a fundamental issue
            raise FileNotFoundError(f"No .npz files found for year {data_year} in {data_root}.")
        random.shuffle(all_files_for_year)

        split_idx = int(len(all_files_for_year) * train_split_ratio)
        train_files_paths = all_files_for_year[:split_idx]
        val_files_paths = all_files_for_year[split_idx:]

        transform = None 

        train_dataset = None
        if train_files_paths: 
            train_dataset = Gremlin(file_list=train_files_paths, transform=transform)
            if train_dataset.xshape is None: # Handles if Gremlin init results in empty dataset
                train_dataset = None 
        
        val_dataset = None
        if val_files_paths: 
            val_dataset = Gremlin(file_list=val_files_paths, transform=transform)
            if val_dataset.xshape is None:
                val_dataset = None

        dataloader_train = None
        # Check if dataset exists AND has samples before creating DataLoader
        if train_dataset and len(train_dataset) > 0: 
            dataloader_train = DataLoader(
                train_dataset, batch_size=batch_size, shuffle=True,
                num_workers=num_workers, pin_memory=True
            )
        
        dataloader_validation = None
        if val_dataset and len(val_dataset) > 0:
            dataloader_validation = DataLoader(
                val_dataset, batch_size=val_batch_size, shuffle=False,
                num_workers=num_workers, pin_memory=True
            )
        
        dataloader_test = None

        max = 60
        min = 0   

        return dataloader_train, dataloader_validation, dataloader_test, max, min 
    ### --------------------- china ----------------------------
    if data_name == 'ca':
        data_root = '/Storage01/ShareData/STR/'
        train_path = os.path.join(data_root , 'train') 
        valid_path = os.path.join(data_root , 'test')

        height,width = in_shape[1], in_shape[2]
        train_sets = RadarSets(train_path, (height, width), mode='train', data_type=data_type)
        valid_sets = RadarSets(valid_path, (height, width), mode='valid', data_type=data_type) # valid_sets 也作为测试集使用
        #valid_sets = RadarSets(valid_path, (height, width), mode='valid', data_type=data_type) # valid_sets 也作为测试集使用
        dataloader_train = DataLoader(train_sets, batch_size=batch_size, num_workers=num_workers,
                                             pin_memory=True, shuffle=True, drop_last=True
                                             )
        dataloader_validation = DataLoader(valid_sets, batch_size=val_batch_size, num_workers=num_workers, # valid_loader 也作为测试集dataloader使用
                                             pin_memory=True, shuffle=False, drop_last=True
                                             )
        dataloader_test = None
        max = 60
        min = 0  
        return dataloader_train, dataloader_validation, dataloader_test, max, min 

# ============================================================================================
# ======================== REFINED AND CONCISE TEST BLOCK ====================================
# ============================================================================================

if __name__ == '__main__':
    # --- 1. 配置测试参数 ---
    # 在这里修改 'us' 或 'ca' 来切换测试目标
    DATASET_TO_TEST = 'us'  
    
    print(f"--- dataloader.py direct test: Testing '{DATASET_TO_TEST}' dataset ---")
    
    # 根据选择的测试目标设置参数
    if DATASET_TO_TEST == 'us':
        config = {
            "batch_size": 4, "val_batch_size": 2, "num_workers": 2,
            "data_name": "us", "in_shape": [4, 768, 1536], "data_type": ""
        }
    elif DATASET_TO_TEST == 'ca':
        config = {
            "batch_size": 4, "val_batch_size": 2, "num_workers": 2,
            "data_name": "ca", "in_shape": [3, 416, 448], "data_type": "vis"
        }
    else:
        raise ValueError(f"Unknown dataset to test: {DATASET_TO_TEST}")

    # --- 2. 加载数据 ---
    try:
        train_loader, vali_loader, test_loader, max_val, min_val = load_data(**config)
    except Exception as e:
        print(f"\n[FATAL ERROR] Failed to call load_data: {e}")
        # 如果加载失败，将所有loader设为None，以安全地完成脚本
        train_loader, vali_loader, test_loader, max_val, min_val = [None] * 5

    # --- 3. 检查并迭代 DataLoader ---
    # 使用一个列表将 loaders 和它们的名字配对，以便循环处理，代码更简洁
    loaders_to_check = [("Train", train_loader), ("Validation", vali_loader), ("Test", test_loader)]

    for name, loader in loaders_to_check:
        print(f"\n--- Checking '{name}' DataLoader ---")
        if loader is None or not loader.dataset:
            print("Result: DataLoader is None or has an empty dataset.")
            continue
        
        print(f"Total samples in dataset: {len(loader.dataset)}")
        try:
            # 只从 loader 中获取第一个 batch 来检查
            input_batch, output_batch = next(iter(loader))
            print(f"Shape from first batch: Input: {input_batch.shape}, Target: {output_batch.shape}")
            print(f"Data type: Input: {input_batch.dtype}, Target: {output_batch.dtype}")
        except StopIteration:
            print("Result: DataLoader is created but yielded no batches.")
            print("  (Check if dataset size < batch size and `drop_last=True`)")
        except Exception as e:
            print(f"[ERROR] Could not get a batch from '{name}' DataLoader: {e}")
            
    # --- 4. 打印其他返回值 ---
    print("\n--- Additional returned values ---")
    print(f"Returned max value: {max_val}")
    print(f"Returned min value: {min_val}")