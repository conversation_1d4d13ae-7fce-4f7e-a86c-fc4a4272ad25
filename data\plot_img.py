from osgeo import gdal
import numpy as np
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature
import cartopy.mpl.ticker as cticker
import cartopy.io.shapereader as shpreader
import matplotlib as mpl
from cartopy.mpl.gridliner import LATITUDE_FORMATTER, LONGITUDE_FORMATTER
import os
from datetime import datetime

# 设置字体
mpl.rcParams['font.family'] = 'Times New Roman'
mpl.use('Agg')  # 使用非交互式后端，适合保存图片

def gen_lat_lon(l_lon: float, r_lon: float, t_lat: float, b_lat: float, res: float):
    """生成经纬度网格"""
    return np.arange(l_lon, r_lon + res / 2, res), np.arange(t_lat, b_lat - res / 2, -1 * res)

def plot_radar_comparison(timestamp, save_dir, pred_dir="predictions", model_name="VCBNet"):
    """绘制一行三列的雷达回波对比图：真实值、预测值、差值"""
    # 生成经纬度网格
    lon1, lat1 = gen_lat_lon(106.12, 124, 42, 25.4, 0.04)
    lon, lat = np.meshgrid(lon1, lat1)

    # 定义数据路径
    real_data_path = f"data/test/{timestamp}_match.npy"
    pred_data_path = f"{pred_dir}/{timestamp}_pred.npy"
    
    # 读取真实数据
    try:
        real_data = np.load(real_data_path)
        real_data = real_data[0] * 60
        real_data[real_data < 10] = np.nan
    except FileNotFoundError:
        print(f"真实数据文件不存在: {real_data_path}")
        return

    # 读取预测数据
    try:
        pred_data = np.load(pred_data_path)
        # 处理预测数据的维度，根据实际保存格式调整
        if pred_data.ndim == 4:  # (batch, channel, height, width)
            pred_data = pred_data[0, 0] * 60
        elif pred_data.ndim == 3:  # (channel, height, width)
            pred_data = pred_data[0] * 60
        else:  # (height, width)
            pred_data = pred_data * 60
        pred_data[pred_data < 10] = np.nan
    except FileNotFoundError:
        print(f"预测数据文件不存在: {pred_data_path}")
        pred_data = np.full_like(real_data, np.nan)

    # 计算差值
    diff_data = pred_data - real_data
    
    # 设置投影和地图边界
    proj = ccrs.Miller()

    # 读取省界和九段线 - 更新为用户指定的路径
    province_shp = r'D:\ZKY\Code\read_tif\shp\bou2_4p.shp'  # 省界shapefile
    line_nine_shp = r'D:\ZKY\Code\read_tif\nine\night_line.shp'  # 九段线shapefile

    try:
        province = shpreader.Reader(province_shp)
        line_nine = shpreader.Reader(line_nine_shp)
    except Exception as e:
        print(f"警告：无法加载shapefile文件: {e}")
        province = None
        line_nine = None
    
    # 创建图形
    fig = plt.figure(figsize=(18, 6), dpi=200)  # 适合1行3列

    # 定义色阶和颜色
    clevs = [i-0.000001 for i in range(-5, 80, 5)]
    cdict = ['#ffffff', "#bae9fe", '#4cc2fe', '#01a0f6', '#00ecec', '#00d800', '#019000',
             '#ffff00', '#e7c000', '#ff9000', '#ff0000', '#d60000', '#c00000', '#ff00f0',
             '#9600b4', '#010101']  # 自定义颜色列表

    # 差值的色阶和颜色（对称的）
    diff_clevs = [i-0.000001 for i in range(-40, 45, 5)]
    diff_cdict = ['#0000ff', '#4169e1', '#87ceeb', '#b0e0e6', '#f0f8ff', '#ffffff',
                  '#fff8dc', '#ffd700', '#ffa500', '#ff6347', '#ff0000', '#dc143c', '#8b0000']

    # 创建子图
    titles = ["Actual", f"{model_name} Prediction", "Difference (Pred - Actual)"]
    data_list = [real_data, pred_data, diff_data]
    
    axes = []
    for i in range(3):
        ax = fig.add_subplot(1, 3, i+1, projection=proj)
        axes.append(ax)

        # 设置地图边界
        ax.set_extent([106.12, 124, 25.4, 42], crs=ccrs.PlateCarree())

        # 添加省界和九段线（如果可用）
        if province is not None:
            ax.add_geometries(province.geometries(), crs=ccrs.PlateCarree(),
                            linewidth=.2, edgecolor='k', facecolor='none')
        if line_nine is not None:
            ax.add_geometries(line_nine.geometries(), crs=ccrs.PlateCarree(),
                            linewidth=.2, edgecolor='k', facecolor='none')

        # 添加标题
        ax.set_title(titles[i], loc='center', fontsize=12)

        # 绘制等值线填充
        if i == 2:  # 差值图使用不同的色阶
            cf = ax.contourf(lon, lat, data_list[i], levels=diff_clevs, colors=diff_cdict,
                           transform=ccrs.PlateCarree(), extend='both')
        else:  # 真实值和预测值使用相同色阶
            cf = ax.contourf(lon, lat, data_list[i], levels=clevs, colors=cdict,
                           transform=ccrs.PlateCarree())

        # 添加网格线
        gl = ax.gridlines(draw_labels=True, crs=ccrs.PlateCarree(), rotate_labels=0,
                        linewidth=0.2, linestyle='--',
                        xlocs=np.arange(105, 125, 5), ylocs=np.arange(25, 45, 5))

        # 只在第一个子图左侧显示纬度标签
        if i == 0:
            gl.ylabels_left = True
        else:
            gl.ylabels_left = False

        # 其他网格线设置
        gl.xlabels_top = False
        gl.ylabels_right = False
        gl.xformatter = LONGITUDE_FORMATTER
        gl.yformatter = LATITUDE_FORMATTER
        gl.xlabel_style = {"size": 8}
        gl.ylabel_style = {"size": 8}
    
    # 添加颜色条
    # 为前两个子图（真实值和预测值）添加共享颜色条
    cbar_ax1 = fig.add_axes([0.35, 0.05, 0.3, 0.02])  # 水平颜色条
    cbar1 = fig.colorbar(axes[1].collections[0], cax=cbar_ax1, orientation='horizontal')
    cbar1.set_label('dBZ', fontsize=10)

    # 为差值图添加单独的颜色条
    cbar_ax2 = fig.add_axes([0.92, 0.15, 0.01, 0.7])  # 垂直颜色条
    cbar2 = fig.colorbar(axes[2].collections[0], cax=cbar_ax2)
    cbar2.set_label('Difference (dBZ)', fontsize=10)

    # 设置整体标题
    fig.suptitle(f'Time: {timestamp} (UTC) - {model_name} Model', fontsize=14)

    # 调整布局
    plt.tight_layout(rect=[0, 0.1, 0.9, 0.95])  # 为colorbar和总标题留出空间
    
    # 创建保存目录（如果不存在）
    os.makedirs(save_dir, exist_ok=True)
    
    # 保存图像
    save_path = os.path.join(save_dir, f"{timestamp}.png")
    plt.savefig(save_path, bbox_inches='tight')
    plt.close()
    
    print(f"成功保存图像: {save_path}")

def main():
    import argparse

    parser = argparse.ArgumentParser(description='绘制雷达回波对比图')
    parser.add_argument('--save_dir', type=str, default='plots', help='图像保存目录')
    parser.add_argument('--pred_dir', type=str, default='predictions', help='预测结果目录')
    parser.add_argument('--test_dir', type=str, default='data/test', help='测试数据目录')
    parser.add_argument('--model_name', type=str, default='VCBNet', help='模型名称')

    args = parser.parse_args()

    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)

    # 获取test目录中所有文件
    test_files = [f for f in os.listdir(args.test_dir) if f.endswith('_match.npy')]

    if not test_files:
        print(f"在 {args.test_dir} 中没有找到测试文件")
        return

    # 提取时间戳
    timestamps = [f.split('_')[0] for f in test_files]

    # 按时间排序
    timestamps.sort()

    print(f"找到 {len(timestamps)} 个时间戳")

    # 处理每个时间戳
    for timestamp in timestamps:
        print(f"处理时间戳: {timestamp}")
        plot_radar_comparison(timestamp, args.save_dir, args.pred_dir, args.model_name)

if __name__ == "__main__":
    main()