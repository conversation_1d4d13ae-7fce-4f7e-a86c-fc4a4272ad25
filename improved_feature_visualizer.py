#!/usr/bin/env python3
"""
改进版VCBNet双分支特征可视化工具
解决了VAN编码器特征缺失和可视化不够直观的问题
"""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import torch.nn.functional as F
from model import VCBNet
import json

class ImprovedVCBNetVisualizer:
    def __init__(self, model_path, device='auto'):
        """
        改进版特征可视化器
        """
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        self.model = self._load_model(model_path)
        self.features = {}
        self._register_comprehensive_hooks()
        
    def _load_model(self, model_path):
        """加载模型"""
        model_dir = os.path.dirname(os.path.dirname(model_path))
        config_path = os.path.join(model_dir, 'model_param.json')
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        model = VCBNet(n_channels=config['in_shape'][0], n_outputs=config['out_chans'])
        
        checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
        state_dict = checkpoint['model']
        
        new_state_dict = {}
        for key, value in state_dict.items():
            if key.startswith('_orig_mod.'):
                new_key = key[10:]
            else:
                new_key = key
            new_state_dict[new_key] = value
        
        model.load_state_dict(new_state_dict, strict=False)
        model.to(self.device)
        model.eval()
        
        return model
    
    def _register_comprehensive_hooks(self):
        """注册更全面的前向钩子"""
        
        def get_activation(name):
            def hook(model, input, output):
                # 处理tuple输出的情况
                if isinstance(output, tuple):
                    # 通常第一个元素是主要的特征张量
                    self.features[name] = output[0].detach() if hasattr(output[0], 'detach') else output[0]
                else:
                    self.features[name] = output.detach() if hasattr(output, 'detach') else output
            return hook
        
        # VAN分支 - 编码器 (简化版，只注册确定可用的层)
        # 我们通过修改forward方法来捕获backbone的输出
        # 这样更安全，避免复杂的钩子注册
        
        # VAN分支 - 解码器
        self.model.up4.register_forward_hook(get_activation('van_decode_up4'))
        self.model.up3.register_forward_hook(get_activation('van_decode_up3'))
        self.model.up2.register_forward_hook(get_activation('van_decode_up2'))
        self.model.up1.register_forward_hook(get_activation('van_decode_up1'))
        
        # U-Net分支 - 编码器
        self.model.conv.register_forward_hook(get_activation('unet_encode_conv'))
        self.model.down1_1.register_forward_hook(get_activation('unet_encode_down1'))
        self.model.down1_2.register_forward_hook(get_activation('unet_encode_down2'))
        self.model.down1_3.register_forward_hook(get_activation('unet_encode_down3'))
        self.model.down1_4.register_forward_hook(get_activation('unet_encode_down4'))
        
        # U-Net分支 - 解码器
        self.model.up1_1.register_forward_hook(get_activation('unet_decode_up1'))
        self.model.up1_2.register_forward_hook(get_activation('unet_decode_up2'))
        self.model.up1_3.register_forward_hook(get_activation('unet_decode_up3'))
        self.model.up1_4.register_forward_hook(get_activation('unet_decode_up4'))
    
    def extract_features(self, input_tensor):
        """提取特征"""
        self.features.clear()
        with torch.no_grad():
            _ = self.model(input_tensor)
        return self.features
    
    def create_feature_summary(self, features, save_dir, sample_name):
        """创建特征概览图"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 分类特征
        van_encode_features = {k: v for k, v in features.items() if 'van_encode' in k}
        van_decode_features = {k: v for k, v in features.items() if 'van_decode' in k}
        unet_encode_features = {k: v for k, v in features.items() if 'unet_encode' in k}
        unet_decode_features = {k: v for k, v in features.items() if 'unet_decode' in k}
        
        # 创建2x2的子图布局
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        
        # 1. VAN编码器
        self._plot_feature_group(van_encode_features, axes[0, 0], "VAN Encoder", 'Blues')
        
        # 2. VAN解码器
        self._plot_feature_group(van_decode_features, axes[0, 1], "VAN Decoder", 'Reds')
        
        # 3. U-Net编码器
        self._plot_feature_group(unet_encode_features, axes[1, 0], "U-Net Encoder", 'Greens')
        
        # 4. U-Net解码器
        self._plot_feature_group(unet_decode_features, axes[1, 1], "U-Net Decoder", 'Purples')
        
        plt.suptitle(f'VCBNet Feature Overview - {sample_name}', fontsize=20)
        plt.tight_layout()
        
        save_path = os.path.join(save_dir, f'{sample_name}_feature_overview.png')
        plt.savefig(save_path, dpi=200, bbox_inches='tight')
        plt.close()
        
        print(f"保存特征概览: {save_path}")
    
    def _plot_feature_group(self, feature_group, ax, title, colormap):
        """绘制特征组"""
        if not feature_group:
            ax.text(0.5, 0.5, 'No Features', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return
        
        # 计算每个特征的统计信息
        stats = []
        labels = []
        
        for name, feature in feature_group.items():
            if feature.dim() == 4:  # (B, C, H, W)
                feat_np = feature[0].cpu().numpy()
                stats.append([
                    np.mean(feat_np),
                    np.std(feat_np),
                    np.max(feat_np),
                    feat_np.shape[0]  # 通道数
                ])
                labels.append(name.replace('van_encode_', '').replace('van_decode_', '')
                            .replace('unet_encode_', '').replace('unet_decode_', ''))
        
        if stats:
            stats = np.array(stats)
            
            # 创建热力图
            im = ax.imshow(stats.T, cmap=colormap, aspect='auto')
            
            # 设置标签
            ax.set_xticks(range(len(labels)))
            ax.set_xticklabels(labels, rotation=45, ha='right')
            ax.set_yticks(range(4))
            ax.set_yticklabels(['Mean', 'Std', 'Max', 'Channels'])
            
            # 添加数值标签
            for i in range(len(labels)):
                for j in range(4):
                    if j == 3:  # 通道数
                        text = f'{int(stats[i, j])}'
                    else:
                        text = f'{stats[i, j]:.2f}'
                    ax.text(i, j, text, ha='center', va='center', 
                           color='white' if stats[i, j] > np.mean(stats[:, j]) else 'black')
            
            plt.colorbar(im, ax=ax, shrink=0.8)
        
        ax.set_title(title, fontsize=14, fontweight='bold')
    
    def visualize_channel_comparison(self, features, save_dir, sample_name, max_channels=12):
        """对比显示VAN和U-Net的对应层特征"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 定义对应关系
        layer_pairs = [
            ('van_decode_up4', 'unet_decode_up1', 'Layer 1 (Deepest)'),
            ('van_decode_up3', 'unet_decode_up2', 'Layer 2'),
            ('van_decode_up2', 'unet_decode_up3', 'Layer 3'),
            ('van_decode_up1', 'unet_decode_up4', 'Layer 4 (Shallowest)')
        ]
        
        for van_layer, unet_layer, layer_name in layer_pairs:
            if van_layer in features and unet_layer in features:
                self._plot_channel_comparison(
                    features[van_layer], features[unet_layer], 
                    van_layer, unet_layer, layer_name,
                    save_dir, sample_name, max_channels
                )
    
    def _plot_channel_comparison(self, van_feat, unet_feat, van_name, unet_name, 
                                layer_name, save_dir, sample_name, max_channels):
        """绘制单层的通道对比"""
        
        van_np = van_feat[0].cpu().numpy()  # (C, H, W)
        unet_np = unet_feat[0].cpu().numpy()  # (C, H, W)
        
        # 选择要显示的通道数
        van_channels = min(van_np.shape[0], max_channels)
        unet_channels = min(unet_np.shape[0], max_channels)
        max_show = max(van_channels, unet_channels)
        
        # 创建子图
        fig, axes = plt.subplots(2, max_show, figsize=(3*max_show, 8))
        if max_show == 1:
            axes = axes.reshape(2, 1)
        
        # 显示VAN分支通道
        for i in range(max_show):
            if i < van_channels:
                im1 = axes[0, i].imshow(van_np[i], cmap='viridis', aspect='auto')
                axes[0, i].set_title(f'VAN Ch{i}')
                plt.colorbar(im1, ax=axes[0, i], shrink=0.8)
            else:
                axes[0, i].axis('off')
            axes[0, i].set_xticks([])
            axes[0, i].set_yticks([])
        
        # 显示U-Net分支通道
        for i in range(max_show):
            if i < unet_channels:
                im2 = axes[1, i].imshow(unet_np[i], cmap='viridis', aspect='auto')
                axes[1, i].set_title(f'U-Net Ch{i}')
                plt.colorbar(im2, ax=axes[1, i], shrink=0.8)
            else:
                axes[1, i].axis('off')
            axes[1, i].set_xticks([])
            axes[1, i].set_yticks([])
        
        # 添加行标签
        axes[0, 0].set_ylabel('VAN Branch', fontsize=12, fontweight='bold')
        axes[1, 0].set_ylabel('U-Net Branch', fontsize=12, fontweight='bold')
        
        plt.suptitle(f'{layer_name} - Channel Comparison\n{sample_name}', fontsize=16)
        plt.tight_layout()
        
        save_path = os.path.join(save_dir, f'{sample_name}_{layer_name.replace(" ", "_")}_channels.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"保存通道对比: {save_path}")
    
    def create_interpretation_guide(self, features, save_dir, sample_name):
        """创建结果解读指南"""
        
        guide_text = f"""# VCBNet特征分析结果解读指南 - {sample_name}

## 🔍 如何看懂特征可视化

### 1. 特征概览图 (feature_overview.png)
这是一个2x2的热力图，显示了四个分支的统计信息：

**颜色含义**:
- 深色 = 高激活值
- 浅色 = 低激活值

**四个象限**:
- 左上: VAN编码器 (特征提取)
- 右上: VAN解码器 (特征重建)  
- 左下: U-Net编码器 (特征提取)
- 右下: U-Net解码器 (特征重建)

**统计指标**:
- Mean: 平均激活强度 (越高表示特征越活跃)
- Std: 激活多样性 (越高表示特征越丰富)
- Max: 最大激活值 (显示特征的动态范围)
- Channels: 通道数 (特征的维度)

### 2. 通道对比图 (*_channels.png)
显示对应层的VAN和U-Net分支的前{max_channels}个通道：

**如何解读**:
- 相似的模式 = 两分支学到了相似特征
- 不同的模式 = 两分支学到了互补特征
- 空白区域 = 该分支在此位置没有重要特征

**颜色含义**:
- 黄色/亮色 = 高激活 (重要特征)
- 蓝色/暗色 = 低激活 (不重要区域)

## 📊 当前分析结果

### 特征统计摘要
"""
        
        # 添加特征统计
        van_decode_features = {k: v for k, v in features.items() if 'van_decode' in k}
        unet_decode_features = {k: v for k, v in features.items() if 'unet_decode' in k}
        
        guide_text += "\n**VAN解码器特征**:\n"
        for name, feat in van_decode_features.items():
            if feat.dim() == 4:
                feat_np = feat[0].cpu().numpy()
                guide_text += f"- {name}: 通道数={feat_np.shape[0]}, 平均激活={np.mean(feat_np):.3f}\n"
        
        guide_text += "\n**U-Net解码器特征**:\n"
        for name, feat in unet_decode_features.items():
            if feat.dim() == 4:
                feat_np = feat[0].cpu().numpy()
                guide_text += f"- {name}: 通道数={feat_np.shape[0]}, 平均激活={np.mean(feat_np):.3f}\n"
        
        guide_text += """

## 💡 改进建议

### 基于特征分析的改进方向:

1. **如果两分支特征很相似**:
   - 考虑参数共享
   - 减少模型冗余
   - 添加多样性正则化

2. **如果两分支特征很不同**:
   - 当前融合策略可能不够好
   - 考虑自适应融合权重
   - 添加交叉注意力机制

3. **如果某分支激活很弱**:
   - 该分支可能没有充分训练
   - 考虑调整损失函数权重
   - 检查数据预处理

## 🎯 下一步分析建议

1. 对比多个样本的特征模式
2. 分析不同天气条件下的特征差异
3. 研究特征与预测精度的关系
4. 设计针对性的改进实验

---
*自动生成于: {sample_name} 分析*
"""
        
        # 保存指南
        guide_path = os.path.join(save_dir, f'{sample_name}_interpretation_guide.md')
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write(guide_text)
        
        print(f"保存解读指南: {guide_path}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='改进版VCBNet特征可视化')
    parser.add_argument('--model_path', type=str, default='721/vcbnet0/checkpoints/checkpoint.pth')
    parser.add_argument('--data_path', type=str, default='data/test/202210090536_match.npy')
    parser.add_argument('--output_dir', type=str, default='improved_feature_analysis')
    parser.add_argument('--max_channels', type=int, default=12, help='每层显示的最大通道数')
    
    args = parser.parse_args()
    
    # 创建可视化器
    visualizer = ImprovedVCBNetVisualizer(args.model_path)
    
    # 加载测试数据
    data = np.load(args.data_path)
    if data.ndim == 3:
        input_tensor = torch.from_numpy(data[:3]).unsqueeze(0).float()
    else:
        input_tensor = torch.from_numpy(data[:, :3]).float()
    
    input_tensor = input_tensor.to(visualizer.device)
    
    # 提取特征
    print("提取中间特征...")
    features = visualizer.extract_features(input_tensor)
    
    sample_name = Path(args.data_path).stem
    
    print(f"提取到 {len(features)} 个特征层")
    for name, feat in features.items():
        if hasattr(feat, 'shape'):
            print(f"  {name}: {feat.shape}")
    
    # 生成可视化
    print("生成特征概览...")
    visualizer.create_feature_summary(features, args.output_dir, sample_name)
    
    print("生成通道对比...")
    visualizer.visualize_channel_comparison(features, args.output_dir, sample_name, args.max_channels)
    
    print("生成解读指南...")
    visualizer.create_interpretation_guide(features, args.output_dir, sample_name)
    
    print(f"\n分析完成！结果保存在: {args.output_dir}")
    print("主要文件:")
    print("- feature_overview.png: 特征概览热力图")
    print("- *_channels.png: 各层通道对比图")
    print("- interpretation_guide.md: 结果解读指南")

if __name__ == "__main__":
    main()
