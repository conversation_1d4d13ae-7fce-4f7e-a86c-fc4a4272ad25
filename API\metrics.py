import numpy as np

#def metric(pred_np, true_np, max, min):
#    pred_np = pred_np * (max - min) + min
#    true_np = true_np * (max - min) + min
#    mse = np.mean((pred_np - true_np) ** 2)
#    mae = np.mean(np.abs(pred_np - true_np))
#    return mse, mae

def metric(pred_norm_np, true_norm_np, max_val, min_val, thresholds=None):
    metrics_results = {}
    pred_denorm_np = pred_norm_np * (max_val - min_val) + min_val
    true_denorm_np = true_norm_np * (max_val - min_val) + min_val
    metrics_results['mse_denorm'] = np.mean((pred_denorm_np - true_denorm_np) ** 2)
    metrics_results['mae_denorm'] = np.mean(np.abs(pred_denorm_np - true_denorm_np))

    if thresholds is None or not thresholds:
        return metrics_results

    pred_norm_flat = pred_norm_np.squeeze(1) 
    true_norm_flat = true_norm_np.squeeze(1) 
    normalized_thresholds_for_comparison = [float(t) / max_val for t in thresholds]

    for original_thresh, norm_thresh_comp in zip(thresholds, normalized_thresholds_for_comparison):
        pred_binary = (pred_norm_flat >= norm_thresh_comp).astype(int)
        target_binary = (true_norm_flat >= norm_thresh_comp).astype(int)
        tp = np.sum((pred_binary == 1) & (target_binary == 1))
        fp = np.sum((pred_binary == 1) & (target_binary == 0))
        fn = np.sum((pred_binary == 0) & (target_binary == 1))
        tn = np.sum((pred_binary == 0) & (target_binary == 0))
        csi = tp / (tp + fp + fn) if (tp + fp + fn) > 0 else 0.0
        pod = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        far = fp / (tp + fp) if (tp + fp) > 0 else 0.0
        hss_numerator = 2 * (tp * tn - fn * fp)
        hss_denominator = ((tp + fn) * (fn + tn)) + ((tp + fp) * (fp + tn))
        hss = hss_numerator / hss_denominator if hss_denominator != 0 else 0.0
        thresh_key_suffix = str(int(original_thresh)) 
        metrics_results[f'csi_{thresh_key_suffix}'] = csi
        metrics_results[f'pod_{thresh_key_suffix}'] = pod
        metrics_results[f'far_{thresh_key_suffix}'] = far
        metrics_results[f'hss_{thresh_key_suffix}'] = hss
    return metrics_results
