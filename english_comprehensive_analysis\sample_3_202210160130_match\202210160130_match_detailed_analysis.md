# 🔬 Detailed Sample Analysis Report - 202210160130_match

## 📊 Feature Statistics Overview

### VAN Branch Statistics
| Layer Name | Channels | Mean Activation | Std Dev | Max Value | Activation Rate(>0.1) |
|------------|----------|-----------------|---------|-----------|----------------------|
| van_encode_stage1 | 64 | 0.0291 | 0.8084 | 5.1524 | 44.88% |
| van_encode_stage2 | 128 | 0.0016 | 0.8041 | 4.7986 | 41.39% |
| van_encode_stage3 | 320 | 0.0286 | 0.5482 | 2.9226 | 45.25% |
| van_encode_stage4 | 512 | 0.0096 | 0.6478 | 11.7537 | 42.96% |
| van_decode_up4 | 256 | 0.0412 | 1.9541 | 18.4921 | 47.11% |
| van_decode_up3 | 128 | -0.0007 | 1.5543 | 9.7621 | 45.53% |
| van_decode_up2 | 64 | 0.0050 | 1.0360 | 15.0338 | 50.34% |
| van_decode_final | 32 | 0.1071 | 0.3251 | 4.6028 | 34.55% |

### U-Net Branch Statistics
| Layer Name | Channels | Mean Activation | Std Dev | Max Value | Activation Rate(>0.1) |
|------------|----------|-----------------|---------|-----------|----------------------|
| unet_encode_conv | 32 | 0.3321 | 0.5182 | 7.4908 | 52.03% |
| unet_encode_down1 | 64 | 0.3344 | 0.7742 | 13.6753 | 32.39% |
| unet_encode_down2 | 128 | 0.3117 | 0.7377 | 10.5843 | 30.29% |
| unet_encode_down3 | 256 | 0.1881 | 0.5502 | 13.2479 | 19.62% |
| unet_encode_down4 | 512 | 0.1409 | 0.3705 | 9.0926 | 19.56% |
| unet_decode_up1 | 256 | 0.1518 | 0.2551 | 4.0995 | 35.50% |
| unet_decode_up2 | 128 | 0.2081 | 0.3537 | 4.9480 | 36.69% |
| unet_decode_up3 | 64 | 0.2387 | 0.4111 | 11.4684 | 38.81% |
| unet_decode_final | 32 | 0.1876 | 0.3904 | 13.8063 | 32.92% |


## 🎯 Inter-layer Similarity Analysis

### Corresponding Layer Similarities
| VAN Layer | U-Net Layer | Cosine Similarity | Pearson Correlation | Interpretation |
|-----------|-------------|-------------------|---------------------|----------------|
| van_decode_up4 | unet_decode_up1 | 0.4057 | 0.1168 | Low similarity, strong complementarity |
| van_decode_up3 | unet_decode_up2 | -0.1008 | -0.2790 | Very low similarity, completely different |
| van_decode_up2 | unet_decode_up3 | 0.2596 | 0.4600 | Low similarity, strong complementarity |
| van_decode_final | unet_decode_final | 0.7656 | 0.3414 | Moderately similar, some complementarity |


## 🗺️ Spatial Activation Pattern Analysis

### Activation Hotspot Distribution
- **van_encode_stage1**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_encode_stage2**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_encode_stage3**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_encode_stage4**: Main activation regions at middle-center, covering 20.1% of spatial area
- **van_decode_up4**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_decode_up3**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_decode_up2**: Main activation regions at lower-center, covering 20.0% of spatial area
- **van_decode_final**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_encode_conv**: Main activation regions at lower-right, covering 20.0% of spatial area
- **unet_encode_down1**: Main activation regions at lower-right, covering 20.0% of spatial area
- **unet_encode_down2**: Main activation regions at lower-right, covering 20.0% of spatial area
- **unet_encode_down3**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_encode_down4**: Main activation regions at middle-center, covering 20.1% of spatial area
- **unet_decode_up1**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_decode_up2**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_decode_up3**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_decode_final**: Main activation regions at middle-center, covering 20.0% of spatial area


## 💡 Single Sample Improvement Suggestions

### Based on Activation Intensity

1. **Strongest Activation Layer**: unet_encode_down1 (activation: 0.3344)
   - This is the most important feature layer of the model
   - Suggestion: Give higher weight in fusion
   
2. **Weakest Activation Layer**: van_decode_up3 (activation: -0.0007)
   - This layer may be under-trained or unimportant for current sample
   - Suggestion: Check training strategy or consider structural adjustment

### Based on Similarity Analysis
- **Moderate Complementarity**: Two branches have both commonalities and differences
- Suggestion: Current fusion strategy is basically reasonable, can fine-tune weights
