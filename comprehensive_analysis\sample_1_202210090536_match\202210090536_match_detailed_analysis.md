# 🔬 详细样本分析报告 - 202210090536_match

## 📊 特征统计概览

### VAN分支统计
| 层名称 | 通道数 | 平均激活 | 标准差 | 最大值 | 激活率(>0.1) |
|--------|--------|----------|--------|--------|-------------|
| van_encode_stage1 | 64 | 0.0378 | 0.7859 | 5.0773 | 45.53% |
| van_encode_stage2 | 128 | 0.0025 | 0.7988 | 4.4574 | 40.39% |
| van_encode_stage3 | 320 | 0.0291 | 0.5474 | 2.8595 | 45.69% |
| van_encode_stage4 | 512 | 0.0123 | 0.6272 | 9.6677 | 42.86% |
| van_decode_up4 | 256 | 0.0502 | 1.9265 | 17.8945 | 47.30% |
| van_decode_up3 | 128 | 0.0010 | 1.5451 | 11.2951 | 45.68% |
| van_decode_up2 | 64 | 0.0764 | 1.0948 | 13.8966 | 53.78% |
| van_decode_final | 32 | 0.1156 | 0.3284 | 4.6900 | 34.57% |

### U-Net分支统计
| 层名称 | 通道数 | 平均激活 | 标准差 | 最大值 | 激活率(>0.1) |
|--------|--------|----------|--------|--------|-------------|
| unet_encode_conv | 32 | 0.5363 | 0.9857 | 8.4647 | 44.37% |
| unet_encode_down1 | 64 | 0.6697 | 1.5456 | 19.8759 | 28.99% |
| unet_encode_down2 | 128 | 0.5462 | 1.4191 | 16.2007 | 26.80% |
| unet_encode_down3 | 256 | 0.1802 | 0.6408 | 20.5644 | 13.61% |
| unet_encode_down4 | 512 | 0.1120 | 0.3416 | 6.6838 | 15.32% |
| unet_decode_up1 | 256 | 0.1527 | 0.2541 | 3.9225 | 36.15% |
| unet_decode_up2 | 128 | 0.2735 | 0.4498 | 5.0611 | 40.17% |
| unet_decode_up3 | 64 | 0.3771 | 0.5568 | 7.4736 | 45.45% |
| unet_decode_final | 32 | 0.1942 | 0.3908 | 7.9982 | 31.27% |


## 🎯 层间相似度分析

### 对应层相似度
| VAN层 | U-Net层 | 余弦相似度 | 皮尔逊相关 | 解释 |
|-------|---------|------------|------------|------|
| van_decode_up4 | unet_decode_up1 | 0.4717 | 0.0636 | 低相似，强互补性 |
| van_decode_up3 | unet_decode_up2 | 0.0355 | 0.0832 | 极低相似，完全不同 |
| van_decode_up2 | unet_decode_up3 | 0.8538 | 0.6998 | 高度相似，可能冗余 |
| van_decode_final | unet_decode_final | 0.8425 | 0.3567 | 高度相似，可能冗余 |


## 🗺️ 空间激活模式分析

### 激活热点分布
- **van_encode_stage1**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_encode_stage2**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_encode_stage3**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_encode_stage4**: 主要激活区域在中部中央，覆盖20.1%的空间
- **van_decode_up4**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_decode_up3**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_decode_up2**: 主要激活区域在下部中央，覆盖20.0%的空间
- **van_decode_final**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_encode_conv**: 主要激活区域在下部中央，覆盖20.0%的空间
- **unet_encode_down1**: 主要激活区域在下部中央，覆盖20.0%的空间
- **unet_encode_down2**: 主要激活区域在下部中央，覆盖20.0%的空间
- **unet_encode_down3**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_encode_down4**: 主要激活区域在中部中央，覆盖20.1%的空间
- **unet_decode_up1**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_decode_up2**: 主要激活区域在下部中央，覆盖20.0%的空间
- **unet_decode_up3**: 主要激活区域在下部中央，覆盖20.0%的空间
- **unet_decode_final**: 主要激活区域在中部中央，覆盖20.0%的空间


## 💡 单样本改进建议

### 基于激活强度的建议

1. **最强激活层**: unet_encode_down1 (激活值: 0.6697)
   - 这是模型最重要的特征层
   - 建议: 在融合时给予更高权重
   
2. **最弱激活层**: van_decode_up3 (激活值: 0.0010)
   - 这层可能训练不充分或对当前样本不重要
   - 建议: 检查训练策略或考虑结构调整

### 基于相似度的建议
- **适度互补**: 两分支既有共同点又有差异
- 建议: 当前融合策略基本合理，可微调权重
