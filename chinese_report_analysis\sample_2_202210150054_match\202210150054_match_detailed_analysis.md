# 🔬 详细样本分析报告 - 202210150054_match

## 📊 特征统计概览

### VAN分支统计
| 层名称 | 通道数 | 平均激活 | 标准差 | 最大值 | 激活率(>0.1) |
|--------|--------|----------|--------|--------|-------------|
| van_encode_stage1 | 64 | 0.0297 | 0.8134 | 5.5267 | 45.29% |
| van_encode_stage2 | 128 | 0.0016 | 0.8056 | 4.8407 | 41.14% |
| van_encode_stage3 | 320 | 0.0286 | 0.5431 | 2.8441 | 45.21% |
| van_encode_stage4 | 512 | 0.0110 | 0.6427 | 11.5733 | 42.93% |
| van_decode_up4 | 256 | 0.0529 | 1.9576 | 18.8860 | 47.29% |
| van_decode_up3 | 128 | -0.0072 | 1.5099 | 11.3319 | 45.78% |
| van_decode_up2 | 64 | -0.0027 | 1.0334 | 12.6751 | 50.20% |
| van_decode_final | 32 | 0.1024 | 0.3139 | 4.9601 | 35.11% |

### U-Net分支统计
| 层名称 | 通道数 | 平均激活 | 标准差 | 最大值 | 激活率(>0.1) |
|--------|--------|----------|--------|--------|-------------|
| unet_encode_conv | 32 | 0.3075 | 0.4482 | 6.7925 | 51.19% |
| unet_encode_down1 | 64 | 0.2970 | 0.6597 | 18.6282 | 31.19% |
| unet_encode_down2 | 128 | 0.2928 | 0.6486 | 8.4412 | 30.11% |
| unet_encode_down3 | 256 | 0.1794 | 0.5210 | 13.0502 | 19.08% |
| unet_encode_down4 | 512 | 0.1352 | 0.3626 | 8.4713 | 18.99% |
| unet_decode_up1 | 256 | 0.1483 | 0.2517 | 3.3615 | 34.79% |
| unet_decode_up2 | 128 | 0.2159 | 0.3647 | 4.7184 | 36.95% |
| unet_decode_up3 | 64 | 0.2379 | 0.4098 | 12.3607 | 38.57% |
| unet_decode_final | 32 | 0.1765 | 0.3785 | 15.8870 | 32.31% |


## 🎯 层间相似度分析

### 对应层相似度
| VAN层 | U-Net层 | 余弦相似度 | 皮尔逊相关 | 解释 |
|-------|---------|------------|------------|------|
| van_decode_up4 | unet_decode_up1 | 0.4904 | 0.1045 | 低相似，强互补性 |
| van_decode_up3 | unet_decode_up2 | -0.1766 | -0.2720 | 极低相似，完全不同 |
| van_decode_up2 | unet_decode_up3 | 0.1079 | 0.3712 | 极低相似，完全不同 |
| van_decode_final | unet_decode_final | 0.7890 | 0.4467 | 中等相似，有一定互补 |


## 🗺️ 空间激活模式分析

### 激活热点分布
- **van_encode_stage1**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_encode_stage2**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_encode_stage3**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_encode_stage4**: 主要激活区域在中部中央，覆盖20.1%的空间
- **van_decode_up4**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_decode_up3**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_decode_up2**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_decode_final**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_encode_conv**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_encode_down1**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_encode_down2**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_encode_down3**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_encode_down4**: 主要激活区域在中部中央，覆盖20.1%的空间
- **unet_decode_up1**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_decode_up2**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_decode_up3**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_decode_final**: 主要激活区域在中部中央，覆盖20.0%的空间


## 💡 单样本改进建议

### 基于激活强度的建议

1. **最强激活层**: unet_encode_conv (激活值: 0.3075)
   - 这是模型最重要的特征层
   - 建议: 在融合时给予更高权重
   
2. **最弱激活层**: van_decode_up3 (激活值: -0.0072)
   - 这层可能训练不充分或对当前样本不重要
   - 建议: 检查训练策略或考虑结构调整

### 基于相似度的建议
- **适度互补**: 两分支既有共同点又有差异
- 建议: 当前融合策略基本合理，可微调权重
