#!/usr/bin/env python3
"""
VCBNet Comprehensive Feature Analysis Tool (English Version)
Deep analysis of multiple samples with detailed improvement suggestions and image analysis
"""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from simple_feature_visualizer import SimpleVCBNetVisualizer
import json
from datetime import datetime

class EnglishComprehensiveAnalyzer:
    def __init__(self, model_path, device='auto'):
        self.visualizer = SimpleVCBNetVisualizer(model_path, device)
        self.all_features = {}
        self.sample_names = []
        
    def analyze_multiple_samples(self, data_paths, output_dir):
        """Analyze multiple samples"""
        os.makedirs(output_dir, exist_ok=True)
        
        print("🔍 Starting comprehensive analysis...")
        
        # Analyze each sample
        for i, data_path in enumerate(data_paths):
            sample_name = Path(data_path).stem
            self.sample_names.append(sample_name)
            
            print(f"\n📊 Analyzing sample {i+1}/{len(data_paths)}: {sample_name}")
            
            # Load data
            data = np.load(data_path)
            if data.ndim == 3:
                input_tensor = torch.from_numpy(data[:3]).unsqueeze(0).float()
            else:
                input_tensor = torch.from_numpy(data[:, :3]).float()
            
            input_tensor = input_tensor.to(self.visualizer.device)
            
            # Extract features
            features = self.visualizer.extract_features_manually(input_tensor)
            self.all_features[sample_name] = features
            
            # Create detailed analysis for each sample
            sample_dir = os.path.join(output_dir, f"sample_{i+1}_{sample_name}")
            self._analyze_single_sample(features, sample_dir, sample_name)
        
        # Cross-sample comparison analysis
        self._cross_sample_analysis(output_dir)
        
        # Generate comprehensive report
        self._generate_comprehensive_report(output_dir)
    
    def _analyze_single_sample(self, features, sample_dir, sample_name):
        """Analyze single sample"""
        os.makedirs(sample_dir, exist_ok=True)
        
        # 1. Generate channel visualization
        self.visualizer.create_comprehensive_channel_view(
            features, sample_dir, sample_name, channels_per_row=6, max_total_channels=24
        )
        
        # 2. Generate branch comparison
        self.visualizer.create_branch_comparison_summary(features, sample_dir, sample_name)
        
        # 3. Generate detailed single sample analysis
        self._create_detailed_sample_analysis(features, sample_dir, sample_name)
    
    def _create_detailed_sample_analysis(self, features, sample_dir, sample_name):
        """Create detailed single sample analysis"""
        
        # Calculate various statistics
        van_stats = self._calculate_branch_stats(features, 'van')
        unet_stats = self._calculate_branch_stats(features, 'unet')
        
        # Calculate layer similarities
        similarities = self._calculate_layer_similarities(features)
        
        # Analyze spatial activation patterns
        spatial_analysis = self._analyze_spatial_patterns(features)
        
        # Generate detailed report
        report = f"""# 🔬 Detailed Sample Analysis Report - {sample_name}

## 📊 Feature Statistics Overview

### VAN Branch Statistics
| Layer Name | Channels | Mean Activation | Std Dev | Max Value | Activation Rate(>0.1) |
|------------|----------|-----------------|---------|-----------|----------------------|
"""
        
        for layer_name, stats in van_stats.items():
            report += f"| {layer_name} | {stats['channels']} | {stats['mean']:.4f} | {stats['std']:.4f} | {stats['max']:.4f} | {stats['activation_rate']:.2%} |\n"
        
        report += f"""
### U-Net Branch Statistics
| Layer Name | Channels | Mean Activation | Std Dev | Max Value | Activation Rate(>0.1) |
|------------|----------|-----------------|---------|-----------|----------------------|
"""
        
        for layer_name, stats in unet_stats.items():
            report += f"| {layer_name} | {stats['channels']} | {stats['mean']:.4f} | {stats['std']:.4f} | {stats['max']:.4f} | {stats['activation_rate']:.2%} |\n"
        
        report += f"""

## 🎯 Inter-layer Similarity Analysis

### Corresponding Layer Similarities
| VAN Layer | U-Net Layer | Cosine Similarity | Pearson Correlation | Interpretation |
|-----------|-------------|-------------------|---------------------|----------------|
"""
        
        for van_layer, unet_layer, cosine_sim, pearson_corr in similarities:
            interpretation = self._interpret_similarity(cosine_sim, pearson_corr)
            report += f"| {van_layer} | {unet_layer} | {cosine_sim:.4f} | {pearson_corr:.4f} | {interpretation} |\n"
        
        report += f"""

## 🗺️ Spatial Activation Pattern Analysis

### Activation Hotspot Distribution
"""
        
        for layer_name, spatial_info in spatial_analysis.items():
            report += f"- **{layer_name}**: Main activation regions at {spatial_info['hotspot_location']}, covering {spatial_info['coverage']:.1%} of spatial area\n"
        
        report += f"""

## 💡 Single Sample Improvement Suggestions

### Based on Activation Intensity
"""
        
        # Find strongest and weakest layers
        all_activations = {}
        for branch_stats in [van_stats, unet_stats]:
            for layer, stats in branch_stats.items():
                all_activations[layer] = stats['mean']
        
        strongest_layer = max(all_activations, key=all_activations.get)
        weakest_layer = min(all_activations, key=all_activations.get)
        
        report += f"""
1. **Strongest Activation Layer**: {strongest_layer} (activation: {all_activations[strongest_layer]:.4f})
   - This is the most important feature layer of the model
   - Suggestion: Give higher weight in fusion
   
2. **Weakest Activation Layer**: {weakest_layer} (activation: {all_activations[weakest_layer]:.4f})
   - This layer may be under-trained or unimportant for current sample
   - Suggestion: Check training strategy or consider structural adjustment

### Based on Similarity Analysis
"""
        
        avg_similarity = np.mean([sim[2] for sim in similarities])
        if avg_similarity > 0.7:
            report += "- **High Similarity Warning**: Two branches have overly similar features, redundancy exists\n- Suggestion: Add diversity regularization or consider parameter sharing\n"
        elif avg_similarity < 0.3:
            report += "- **Strong Complementarity**: Two branches learned very different features\n- Suggestion: Optimize fusion strategy, consider adaptive weights\n"
        else:
            report += "- **Moderate Complementarity**: Two branches have both commonalities and differences\n- Suggestion: Current fusion strategy is basically reasonable, can fine-tune weights\n"
        
        # Save report
        report_path = os.path.join(sample_dir, f"{sample_name}_detailed_analysis.md")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"  Saved detailed analysis: {report_path}")
    
    def _calculate_branch_stats(self, features, branch_prefix):
        """Calculate branch statistics"""
        stats = {}
        
        for layer_name, feature in features.items():
            if branch_prefix in layer_name and feature.dim() == 4:
                feat_np = feature[0].cpu().numpy()
                
                stats[layer_name] = {
                    'channels': feat_np.shape[0],
                    'mean': np.mean(feat_np),
                    'std': np.std(feat_np),
                    'max': np.max(feat_np),
                    'min': np.min(feat_np),
                    'activation_rate': np.mean(feat_np > 0.1)  # Activation rate
                }
        
        return stats
    
    def _calculate_layer_similarities(self, features):
        """Calculate inter-layer similarities"""
        similarities = []
        
        layer_pairs = [
            ('van_decode_up4', 'unet_decode_up1'),
            ('van_decode_up3', 'unet_decode_up2'),
            ('van_decode_up2', 'unet_decode_up3'),
            ('van_decode_final', 'unet_decode_final')
        ]
        
        for van_layer, unet_layer in layer_pairs:
            if van_layer in features and unet_layer in features:
                van_feat = features[van_layer][0].cpu().numpy()
                unet_feat = features[unet_layer][0].cpu().numpy()
                
                # Calculate average activation maps
                van_avg = np.mean(van_feat, axis=0)
                unet_avg = np.mean(unet_feat, axis=0)
                
                # Flatten
                van_flat = van_avg.flatten()
                unet_flat = unet_avg.flatten()
                
                # Cosine similarity
                cosine_sim = np.dot(van_flat, unet_flat) / (np.linalg.norm(van_flat) * np.linalg.norm(unet_flat))
                
                # Pearson correlation coefficient
                pearson_corr = np.corrcoef(van_flat, unet_flat)[0, 1]
                
                similarities.append((van_layer, unet_layer, cosine_sim, pearson_corr))
        
        return similarities
    
    def _analyze_spatial_patterns(self, features):
        """Analyze spatial activation patterns"""
        spatial_analysis = {}
        
        for layer_name, feature in features.items():
            if feature.dim() == 4:
                feat_np = feature[0].cpu().numpy()
                avg_activation = np.mean(feat_np, axis=0)
                
                # Find activation hotspots
                threshold = np.percentile(avg_activation, 80)
                hotspots = avg_activation > threshold
                
                # Calculate hotspot location
                hotspot_indices = np.where(hotspots)
                if len(hotspot_indices[0]) > 0:
                    center_y = np.mean(hotspot_indices[0])
                    center_x = np.mean(hotspot_indices[1])
                    
                    # Determine location description
                    h, w = avg_activation.shape
                    if center_y < h/3:
                        y_desc = "upper"
                    elif center_y > 2*h/3:
                        y_desc = "lower"
                    else:
                        y_desc = "middle"
                    
                    if center_x < w/3:
                        x_desc = "left"
                    elif center_x > 2*w/3:
                        x_desc = "right"
                    else:
                        x_desc = "center"
                    
                    location = f"{y_desc}-{x_desc}"
                else:
                    location = "scattered"
                
                spatial_analysis[layer_name] = {
                    'hotspot_location': location,
                    'coverage': np.mean(hotspots)
                }
        
        return spatial_analysis
    
    def _interpret_similarity(self, cosine_sim, pearson_corr):
        """Interpret similarity values"""
        if cosine_sim > 0.8:
            return "Highly similar, possible redundancy"
        elif cosine_sim > 0.5:
            return "Moderately similar, some complementarity"
        elif cosine_sim > 0.2:
            return "Low similarity, strong complementarity"
        else:
            return "Very low similarity, completely different"
    
    def _cross_sample_analysis(self, output_dir):
        """Cross-sample comparison analysis"""
        print("\n📈 Performing cross-sample comparison analysis...")
        
        # Collect statistics from all samples
        all_sample_stats = {}
        
        for sample_name, features in self.all_features.items():
            van_stats = self._calculate_branch_stats(features, 'van')
            unet_stats = self._calculate_branch_stats(features, 'unet')
            similarities = self._calculate_layer_similarities(features)
            
            all_sample_stats[sample_name] = {
                'van_stats': van_stats,
                'unet_stats': unet_stats,
                'similarities': similarities
            }
        
        # Generate cross-sample comparison plots
        self._create_cross_sample_plots(all_sample_stats, output_dir)
    
    def _create_cross_sample_plots(self, all_sample_stats, output_dir):
        """Create cross-sample comparison plots"""
        
        # 1. Activation intensity comparison
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # VAN branch activation intensity
        van_data = []
        unet_data = []
        
        for sample_name, stats in all_sample_stats.items():
            for layer_name, layer_stats in stats['van_stats'].items():
                van_data.append([sample_name, layer_name, layer_stats['mean']])
            
            for layer_name, layer_stats in stats['unet_stats'].items():
                unet_data.append([sample_name, layer_name, layer_stats['mean']])
        
        # Plot heatmaps
        self._plot_activation_heatmap(van_data, axes[0, 0], "VAN Branch Activation Intensity")
        self._plot_activation_heatmap(unet_data, axes[0, 1], "U-Net Branch Activation Intensity")
        
        # 2. Similarity trends
        similarity_trends = {}
        for sample_name, stats in all_sample_stats.items():
            similarities = [sim[2] for sim in stats['similarities']]
            similarity_trends[sample_name] = similarities
        
        layer_labels = ['Layer1', 'Layer2', 'Layer3', 'Layer4']
        for sample_name, similarities in similarity_trends.items():
            axes[1, 0].plot(layer_labels, similarities, 'o-', label=sample_name, linewidth=2)
        
        axes[1, 0].set_title('Cross-Sample Similarity Trends')
        axes[1, 0].set_ylabel('Cosine Similarity')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 3. Branch balance analysis
        branch_balance = {}
        for sample_name, stats in all_sample_stats.items():
            van_avg = np.mean([s['mean'] for s in stats['van_stats'].values()])
            unet_avg = np.mean([s['mean'] for s in stats['unet_stats'].values()])
            branch_balance[sample_name] = {'VAN': van_avg, 'U-Net': unet_avg}
        
        samples = list(branch_balance.keys())
        van_values = [branch_balance[s]['VAN'] for s in samples]
        unet_values = [branch_balance[s]['U-Net'] for s in samples]
        
        x = np.arange(len(samples))
        width = 0.35
        
        axes[1, 1].bar(x - width/2, van_values, width, label='VAN', alpha=0.8)
        axes[1, 1].bar(x + width/2, unet_values, width, label='U-Net', alpha=0.8)
        axes[1, 1].set_title('Branch Balance Comparison')
        axes[1, 1].set_ylabel('Average Activation Intensity')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels(samples, rotation=45)
        axes[1, 1].legend()
        
        plt.tight_layout()
        save_path = os.path.join(output_dir, 'cross_sample_analysis.png')
        plt.savefig(save_path, dpi=200, bbox_inches='tight')
        plt.close()
        
        print(f"Saved cross-sample analysis plot: {save_path}")
    
    def _plot_activation_heatmap(self, data, ax, title):
        """Plot activation intensity heatmap"""
        if not data:
            return
        
        # Convert data format
        samples = list(set([d[0] for d in data]))
        layers = list(set([d[1] for d in data]))
        
        matrix = np.zeros((len(layers), len(samples)))
        
        for i, layer in enumerate(layers):
            for j, sample in enumerate(samples):
                for d in data:
                    if d[0] == sample and d[1] == layer:
                        matrix[i, j] = d[2]
                        break
        
        im = ax.imshow(matrix, cmap='viridis', aspect='auto')
        ax.set_xticks(range(len(samples)))
        ax.set_xticklabels(samples, rotation=45)
        ax.set_yticks(range(len(layers)))
        ax.set_yticklabels(layers)
        ax.set_title(title)
        
        # Add value labels
        for i in range(len(layers)):
            for j in range(len(samples)):
                ax.text(j, i, f'{matrix[i, j]:.3f}', ha='center', va='center',
                       color='white' if matrix[i, j] > np.mean(matrix) else 'black',
                       fontsize=8)
        
        plt.colorbar(im, ax=ax, shrink=0.8)

    def _generate_comprehensive_report(self, output_dir):
        """Generate comprehensive analysis report"""
        print("\n📝 Generating comprehensive analysis report...")

        # Collect all statistics
        all_stats = {}
        all_similarities = []

        for sample_name, features in self.all_features.items():
            van_stats = self._calculate_branch_stats(features, 'van')
            unet_stats = self._calculate_branch_stats(features, 'unet')
            similarities = self._calculate_layer_similarities(features)

            all_stats[sample_name] = {
                'van_stats': van_stats,
                'unet_stats': unet_stats,
                'similarities': similarities
            }
            all_similarities.extend([sim[2] for sim in similarities])

        # Calculate overall statistics
        avg_similarity = np.mean(all_similarities)
        std_similarity = np.std(all_similarities)

        # Calculate branch balance
        van_activations = []
        unet_activations = []

        for stats in all_stats.values():
            van_activations.extend([s['mean'] for s in stats['van_stats'].values()])
            unet_activations.extend([s['mean'] for s in stats['unet_stats'].values()])

        van_avg = np.mean(van_activations)
        unet_avg = np.mean(unet_activations)
        balance_ratio = van_avg / unet_avg if unet_avg > 0 else 0

        # Generate report
        report = f"""# 🔬 VCBNet Dual-Branch Comprehensive Analysis Report

## 📋 Analysis Overview
- **Analysis Time**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Number of Samples**: {len(self.sample_names)}
- **Model**: VCBNet Dual-Branch Architecture
- **Analyzed Samples**: {', '.join(self.sample_names)}

## 🎯 Key Findings

### 1. Branch Balance Analysis
- **VAN Branch Average Activation**: {van_avg:.4f}
- **U-Net Branch Average Activation**: {unet_avg:.4f}
- **Balance Ratio**: {balance_ratio:.4f} (VAN/U-Net)

**Conclusion**: {'U-Net branch dominates' if balance_ratio < 0.5 else 'VAN branch dominates' if balance_ratio > 2 else 'Branches are relatively balanced'}

### 2. Feature Similarity Analysis
- **Average Similarity**: {avg_similarity:.4f} ± {std_similarity:.4f}
- **Similarity Range**: {min(all_similarities):.4f} - {max(all_similarities):.4f}

**Interpretation**: {'High similarity, feature redundancy exists' if avg_similarity > 0.7 else 'Low similarity, strong complementarity' if avg_similarity < 0.3 else 'Moderate similarity, appropriate complementarity'}

## 📊 Detailed Analysis Results

### Sample Feature Comparison
| Sample | VAN Avg Activation | U-Net Avg Activation | Avg Similarity | Dominant Branch |
|--------|-------------------|---------------------|----------------|-----------------|
"""

        for sample_name, stats in all_stats.items():
            van_avg_sample = np.mean([s['mean'] for s in stats['van_stats'].values()])
            unet_avg_sample = np.mean([s['mean'] for s in stats['unet_stats'].values()])
            sample_similarity = np.mean([sim[2] for sim in stats['similarities']])
            dominant = 'U-Net' if unet_avg_sample > van_avg_sample else 'VAN'

            report += f"| {sample_name} | {van_avg_sample:.4f} | {unet_avg_sample:.4f} | {sample_similarity:.4f} | {dominant} |\n"

        report += f"""

### Layer Analysis
#### Strongest Activation Layers (Across All Samples)
"""

        # Find strongest layers
        all_layer_activations = {}
        for stats in all_stats.values():
            for branch_stats in [stats['van_stats'], stats['unet_stats']]:
                for layer_name, layer_stats in branch_stats.items():
                    if layer_name not in all_layer_activations:
                        all_layer_activations[layer_name] = []
                    all_layer_activations[layer_name].append(layer_stats['mean'])

        # Calculate average activations
        avg_layer_activations = {k: np.mean(v) for k, v in all_layer_activations.items()}
        sorted_layers = sorted(avg_layer_activations.items(), key=lambda x: x[1], reverse=True)

        for i, (layer_name, avg_activation) in enumerate(sorted_layers[:5]):
            report += f"{i+1}. **{layer_name}**: {avg_activation:.4f}\n"

        report += f"""

## 🔧 Detailed Improvement Recommendations

### 1. Branch Balance Optimization
"""

        if balance_ratio < 0.3:
            report += """
**Issue**: VAN branch significantly weaker than U-Net branch
**Recommendations**:
- Increase learning rate for VAN branch
- Adjust loss function weights to give more attention to VAN branch
- Check if VAN backbone pretrained weights are correctly loaded
- Consider feature normalization for VAN branch
"""
        elif balance_ratio > 3:
            report += """
**Issue**: VAN branch too strong, U-Net branch contribution insufficient
**Recommendations**:
- Increase complexity of U-Net branch
- Adjust fusion weights to give U-Net higher weight
- Check if U-Net branch training is sufficient
"""
        else:
            report += """
**Status**: Branch balance is good
**Recommendations**:
- Maintain current training strategy
- Can try fine-tuning fusion weights for further optimization
"""

        report += f"""

### 2. Fusion Strategy Optimization
"""

        if avg_similarity > 0.7:
            report += """
**Issue**: Feature similarity too high, redundancy exists
**Recommendations**:
- Implement adaptive fusion weights: `weight = 1 / (1 + similarity)`
- Add diversity regularization loss
- Consider parameter sharing to reduce redundancy
- Introduce adversarial training to promote feature diversity
"""
        elif avg_similarity < 0.2:
            report += """
**Advantage**: Features highly complementary, but fusion may be insufficient
**Recommendations**:
- Implement cross-attention mechanism
- Use gated fusion: `out = gate * van_out + (1-gate) * unet_out`
- Add feature alignment module
- Consider multi-scale fusion strategy
"""
        else:
            report += """
**Status**: Feature complementarity is moderate
**Recommendations**:
- Current fusion strategy is basically reasonable
- Can try learnable fusion weights
- Consider adding attention mechanism for further optimization
"""

        report += f"""

### 3. Architecture Improvement Suggestions

#### Based on Activation Patterns
- **Strongest Layer Optimization**: Add more refined feature extraction for {sorted_layers[0][0]} layer
- **Weakest Layer Improvement**: Consider structural adjustment or training strategy improvement for weakest activation layers

#### Specific Implementation Suggestions
```python
# 1. Adaptive Fusion Weights
class AdaptiveFusion(nn.Module):
    def __init__(self, channels):
        super().__init__()
        self.weight_net = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels*2, channels//4, 1),
            nn.ReLU(),
            nn.Conv2d(channels//4, 1, 1),
            nn.Sigmoid()
        )

    def forward(self, van_feat, unet_feat):
        combined = torch.cat([van_feat, unet_feat], dim=1)
        weight = self.weight_net(combined)
        return weight * van_feat + (1 - weight) * unet_feat

# 2. Cross-Attention Mechanism
class CrossAttention(nn.Module):
    def __init__(self, channels):
        super().__init__()
        self.query = nn.Conv2d(channels, channels//8, 1)
        self.key = nn.Conv2d(channels, channels//8, 1)
        self.value = nn.Conv2d(channels, channels, 1)
        self.softmax = nn.Softmax(dim=-1)

    def forward(self, van_feat, unet_feat):
        B, C, H, W = van_feat.shape

        q = self.query(van_feat).view(B, -1, H*W).permute(0, 2, 1)
        k = self.key(unet_feat).view(B, -1, H*W)
        v = self.value(unet_feat).view(B, -1, H*W).permute(0, 2, 1)

        attention = self.softmax(torch.bmm(q, k))
        enhanced = torch.bmm(attention, v).permute(0, 2, 1).view(B, C, H, W)

        return van_feat + enhanced

# 3. Feature Alignment Module
class FeatureAlignment(nn.Module):
    def __init__(self, channels):
        super().__init__()
        self.align_conv = nn.Conv2d(channels, channels, 3, padding=1)
        self.norm = nn.BatchNorm2d(channels)

    def forward(self, feat1, feat2):
        aligned_feat1 = self.norm(self.align_conv(feat1))
        return aligned_feat1, feat2
```

## 📈 Image Analysis Interpretation

### Cross-Sample Analysis Plot (cross_sample_analysis.png)
1. **Top-Left - VAN Branch Activation Heatmap**:
   - Brighter colors indicate stronger activation
   - Shows which layers perform best on which samples

2. **Top-Right - U-Net Branch Activation Heatmap**:
   - Compare with VAN branch to observe activation pattern differences

3. **Bottom-Left - Similarity Trends**:
   - Shows similarity variation across layers for different samples
   - Flat lines indicate good stability, large fluctuations indicate sample sensitivity

4. **Bottom-Right - Branch Balance**:
   - Visually shows contribution comparison between two branches
   - Ideally, two bars should be similar in height

### Individual Sample Detailed Analysis Images
Each sample directory contains:
- **Channel Images**: Show feature patterns of each layer, yellow regions are important features
- **Branch Comparison Images**: Show spatial activation patterns and similarity of corresponding layers

## 🎯 Action Plan

### Short-term Improvements (1-2 weeks)
1. Implement adaptive fusion weights
2. Adjust training strategy to balance two branches
3. Add feature diversity regularization

### Medium-term Improvements (1 month)
1. Design cross-attention mechanism
2. Implement multi-scale fusion
3. Optimize network structure

### Long-term Research (2-3 months)
1. Explore new fusion architectures
2. Research dynamic weight allocation
3. Consider advanced techniques like knowledge distillation

## 📋 Specific Recommendations Based on Current Analysis

### Critical Issues Found:
"""

        # Add specific issues based on analysis
        critical_issues = []

        if balance_ratio < 0.3:
            critical_issues.append("**Severe Branch Imbalance**: VAN branch severely underutilized")

        if avg_similarity > 0.8:
            critical_issues.append("**High Feature Redundancy**: Two branches learning similar features")

        if std_similarity > 0.2:
            critical_issues.append("**High Similarity Variance**: Inconsistent feature patterns across samples")

        if critical_issues:
            for issue in critical_issues:
                report += f"- {issue}\n"
        else:
            report += "- No critical issues detected, model architecture is reasonable\n"

        report += f"""

### Priority Actions:
1. **Immediate**: {'Balance branch training' if balance_ratio < 0.5 else 'Optimize fusion strategy'}
2. **Next**: {'Reduce feature redundancy' if avg_similarity > 0.7 else 'Enhance feature complementarity'}
3. **Future**: Implement advanced fusion mechanisms

---
*Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*Analysis Tool Version: English Comprehensive v1.0*
"""

        # Save report
        report_path = os.path.join(output_dir, 'comprehensive_report.md')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"Saved comprehensive report: {report_path}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='VCBNet Comprehensive Feature Analysis')
    parser.add_argument('--model_path', type=str, default='721/vcbnet0/checkpoints/checkpoint.pth')
    parser.add_argument('--data_dir', type=str, default='data/test')
    parser.add_argument('--output_dir', type=str, default='english_comprehensive_analysis')
    parser.add_argument('--num_samples', type=int, default=3, help='Number of samples to analyze')
    
    args = parser.parse_args()
    
    # Get test files
    test_files = list(Path(args.data_dir).glob('*_match.npy'))[:args.num_samples]
    
    if len(test_files) < args.num_samples:
        print(f"Warning: Only found {len(test_files)} files, less than requested {args.num_samples}")
    
    print(f"🎯 Starting comprehensive analysis of {len(test_files)} samples...")
    for i, file in enumerate(test_files):
        print(f"  Sample {i+1}: {file.name}")
    
    # Create analyzer
    analyzer = EnglishComprehensiveAnalyzer(args.model_path)
    
    # Execute analysis
    analyzer.analyze_multiple_samples([str(f) for f in test_files], args.output_dir)
    
    print(f"\n🎉 Comprehensive analysis completed! Results saved in: {args.output_dir}")

if __name__ == "__main__":
    main()
