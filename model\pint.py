import torch
import torch.nn as nn
import torch.nn.functional as F



def fill_fc_weights(layers):
    for m in layers.modules():
        if isinstance(m, nn.Conv2d):
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
# --- 依赖项的简化占位符 (与之前相同) ---
class CNA3x3(nn.Module):
    def __init__(self, in_ch, out_ch):
        super(CNA3x3, self).__init__()
        self.conv = nn.Sequential(nn.Conv2d(in_ch, out_ch, 3, padding=1, bias=False), nn.BatchNorm2d(out_ch), nn.ReLU(inplace=True))
    def forward(self, x): return self.conv(x)

# class MLP(nn.Module):
#     def __init__(self, in_ch, out_ch, mid_ch=None):
#         super().__init__()
#         if mid_ch is None: mid_ch = in_ch * 2
#         self.fc1 = nn.Conv2d(in_ch, mid_ch, 1)
#         self.act = nn.ReLU()
#         self.fc2 = nn.Conv2d(mid_ch, out_ch, 1)
#     def forward(self, x): return self.fc2(self.act(self.fc1(x)))


class MLP(nn.Module):
    """
    Implementation of MLP with 1*1 convolutions.
    Input: tensor with shape [B, C, H, W]
    """

    def __init__(self, in_ch,out_ch, mid_ch=None,act_layer=nn.GELU, drop=0.0):
        super().__init__()
        if mid_ch is None: mid_ch = in_ch * 2
        self.fc1 = nn.Conv2d(in_ch, mid_ch, 1)
        self.act = act_layer()
        self.fc2 = nn.Conv2d(mid_ch, out_ch, 1)
        self.drop = nn.Dropout(drop)
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Conv2d):
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x

def scaled_dot_product_attention(query, key, value):
    temp = query.bmm(key.transpose(1, 2))
    scale = query.size(-1) ** 0.5
    softmax = F.softmax(temp / scale, dim=-1)
    return softmax.bmm(value)

class CrossAttention(nn.Module):
    def __init__(self, qv_in_channels, k_in_channels):
        super(CrossAttention, self).__init__()
        self.conv_query = nn.Conv2d(qv_in_channels, qv_in_channels, 1)
        self.conv_key = nn.Conv2d(k_in_channels, k_in_channels, 1)
        self.conv_value = nn.Conv2d(qv_in_channels, qv_in_channels, 1)
        self.conv_out = nn.Conv2d(qv_in_channels, qv_in_channels, 1)
        fill_fc_weights(self)
    def forward(self, q, v):
        H, W = q.shape[2], q.shape[3]
        query = self.conv_query(q).view(q.size(0), q.size(1), -1)
        key = self.conv_key(v).view(v.size(0), v.size(1), -1)
        value = self.conv_value(q).view(q.size(0), q.size(1), -1)
        out = scaled_dot_product_attention(query, key, value)
        out = out.view(out.size(0), out.size(1), H, W)
        return self.conv_out(out)

class Pint(nn.Module):
    """
    最终设计的模块，在SpectralHint的基础上，增加了最后的残差连接。
    模块的输出 = 增强特征 + 原始特征
    """
    def __init__(self, in_channels: int, out_channels: int, mid_channels: int):
        super().__init__()
        
        # --- 内部处理模块 ---
        self.x_proj = CNA3x3(in_channels, mid_channels)
        self.spectral_mixer = nn.Conv2d(in_channels, mid_channels, kernel_size=1)
        self.cross_attn = CrossAttention(qv_in_channels=mid_channels, k_in_channels=mid_channels)
        self.fusion_norm = nn.BatchNorm2d(mid_channels)
        self.final_mlp = MLP(in_ch=mid_channels, out_ch=out_channels)
        
        # --- 【新增】用于最终残差连接的投影层 ---
        # 它的作用是将原始的多通道输入x，直接投影到最终的输出通道数(3)
        self.initial_feature_proj = nn.Conv2d(in_channels, out_channels, kernel_size=1)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 1. 提取空间特征和光谱特征
        x_d = self.x_proj(x)
        hint = self.spectral_mixer(x)

        # 2. 交叉注意力融合
        attn_out = self.cross_attn(x_d, hint)

        # 3. 第一次残差连接与归一化
        fused_features = self.fusion_norm(x_d + attn_out)

        # 4. 经过MLP，得到“增强特征”
        enhanced_feature = self.final_mlp(fused_features)

        # 5. 【新增】将原始输入投影到目标维度，用于最终的残差连接
        initial_feature_processed = self.initial_feature_proj(x)
        
        # 6. 【新增】最终的残差连接：输出 = 增强特征 + 原始特征
        final_output = enhanced_feature + initial_feature_processed 
        #final_output = torch.cat([enhanced_feature,initial_feature_processed],dim=1) 这里是caoncat做法
        
        return final_output
if __name__ == "__main__":
    # 1. 定义测试参数
    BATCH_SIZE = 4
    INPUT_CHANNELS = 5
    IMG_SIZE = 512
    MID_CHANNELS = 64
    FINAL_OUT_CHANNELS = 3

    # 2. 创建模拟输入数据
    satellite_input = torch.randn(BATCH_SIZE, INPUT_CHANNELS, IMG_SIZE, IMG_SIZE)

    print("=" * 60)
    print("Testing Final Design: SpectralHint with Final Residual Connection")
    print("=" * 60)
    print(f"Input Satellite Data Shape:   {satellite_input.shape}")

    # 3. 测试我们最终设计的、带有最终残差连接的模块
    final_model = SpectralHintWithResidual(
        in_channels=INPUT_CHANNELS,
        out_channels=FINAL_OUT_CHANNELS,
        mid_channels=MID_CHANNELS
    )
    final_output = final_model(satellite_input)
    print(f"Final Model Output Shape:       {final_output.shape}")

    # 4. 验证输出维度是否正确
    assert final_output.shape == (BATCH_SIZE, FINAL_OUT_CHANNELS, IMG_SIZE, IMG_SIZE)
    print("[SUCCESS] The final output shape is correct!")
    print("          The model now learns the 'enhancement' signal.")
    print("=" * 60)