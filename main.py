import argparse
from exp import Exp
import warnings
warnings.filterwarnings('ignore')

def create_parser():
    parser = argparse.ArgumentParser()
    # 基本参数
    parser.add_argument('--res_dir', default='./721', type=str)
    parser.add_argument('--ex_name', default='vcbnet3_1', type=str)
    parser.add_argument('--seed', default=42, type=int)
    
    # 数据参数
    parser.add_argument('--batch_size', default=8, type=int)
    parser.add_argument('--val_batch_size', default=8, type=int)
    parser.add_argument('--test_batch_size', default=1, type=int, help="测试时的批次大小，设为1则指标为算术平均") # 新增参数
    parser.add_argument('--data_name', default='ca', choices=['us', 'ca'])
    ## data_type参数只有 china 数据有，us数据只有ir 
    parser.add_argument('--data_type', default='vis', choices=['ir', 'vis'])
    parser.add_argument('--num_workers', default=16, type=int)
    ###  模型输入的 szie ，请手动设置  
    ###   us+year :  [4, 768, 1536]   对应 unet  vit s-vit
    ###   us+year :  [3, 768, 1536]   对应 bcunet vcbnet ，需要在  Gremlin 中修改
    ###   ca_small :  [B, 416, 448]   
    parser.add_argument('--in_shape', default=[3, 416, 448], type=int, nargs='*')
    # 模型类型选择, 如果是 bcunet 和 vcbnet，则 模型设置全在 exp中,　输入为3通道！
    parser.add_argument('--model_type', default='vcbnet3', type=str, choices=['vcbnet3_wt','vcbnet3','unet', 'vit', 's-vit','bcunet','vcbnet','vcbnet1','vcbnet2','vunet','vcbcnet','vcbnet_fusion' ], help="选择使用的模型类型")
    
    # 通用模型参数
    parser.add_argument('--out_chans', default=1, type=int)
    
    # UNet 模型参数   ##  [32,64,128,256]
    parser.add_argument('--unet_channels', default=[16,32,64,128,256], type=int, nargs='*') #[32, 64, 128, 256] ,[32,64,128,256,512]
    parser.add_argument('--unet_skip', default=True, type=bool)
    
    # ViT 模型参数 (shared by both vit and s-vit)
    #parser.add_argument('--vit_patch_size', default=16, type=int, help="ViT模型的patch大小")
    #parser.add_argument('--vit_dim', default=512, type=int, help="ViT模型的特征维度")
    #parser.add_argument('--vit_depth', default=4, type=int, help="ViT模型的Transformer深度")
    #parser.add_argument('--vit_heads', default=8, type=int, help="ViT模型的注意力头数")
    #parser.add_argument('--vit_mlp_dim', default=128, type=int, help="ViT模型的MLP维度")
    #parser.add_argument('--vit_dim_head', default=64, type=int, help="ViT模型的每个头的维度")
    
    # S-ViT specific parameters (smoother parameters)
    #parser.add_argument('--freeze_vit', default=False, type=bool,
    #                    help='Freeze ViT backbone weights in S-ViT model')
    #parser.add_argument('--load_pretrained', default=False, type=bool,
    #                   help='Load pretrained weights for ViT backbone')
    #parser.add_argument('--pretrained_path', type=str, default=None,
    #                    help='Path to pretrained weights file')
    #parser.add_argument('--hidden_layers', type=int, nargs='+', default=[32, 16],
    #                    help='Hidden layer dimensions for smoother (e.g., --hidden_layers 32 16)')
    
    # 训练参数
    parser.add_argument('--epochs', default=100, type=int)
    parser.add_argument('--log_step', default=1, type=int)
    parser.add_argument('--lr', default=0.001, type=float)
    parser.add_argument('--early_stopping_patience', default=10, type=int)
    parser.add_argument('--scheduler', default='cosine', type=str, choices=['constant', 'linear', 'cosine'])
    parser.add_argument('--optimizer', default='adamw', type=str, choices=['adam', 'adamw', 'lion', 'sgd'])
    parser.add_argument('--warmup_steps', default=1000, type=int)
    parser.add_argument('--eval_step', default=20, type=int, help="每隔多少个epoch进行一次完整的验证指标计算和打印") # 新增参数
    # loss函数选择
    parser.add_argument('--loss_type', default='awmse', type=str, 
                        choices=['mse', 'mae', 'wmse', 'wmae', 'hmse','hmae','awmse','pod','3wmse'],
                        help="Type of loss function to use (mse, mae, wmse, wmae，hmse, hmae).")
    #parser.add_argument('--loss_alpha', default=0.5, type=float,
    #                   help="Weight for the regression component (HMAE or MAE) in combined losses.")
    # 测试指标
    parser.add_argument('--metrics_thresholds', default=[10,20,30,40], type=int, nargs='*', 
                        help="用于计算 CSI, POD 等指标的原始物理尺度整数阈值列表。")  ##[10,20,30,40]
    # 混合精度训练参数
    parser.add_argument('--mixed_precision', type=str, default='bf16', choices=['no', 'fp16', 'bf16'])
    
    # 分布式训练参数
    parser.add_argument('--gradient_accumulation_steps', type=int, default=1)
    parser.add_argument('--gradient_clipping', type=float, default=1.0)

    return parser

if __name__ == '__main__':
    args = create_parser().parse_args()
    exp = Exp(args)
    print('>>>>>>>>>>>>>>>>>>>>>>>>>>>>  开始训练  <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<')
    exp.train(args)
    print('>>>>>>>>>>>>>>>>>>>>>>>>>>>> 开始测试 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<')
    test_loss = exp.test(args)
