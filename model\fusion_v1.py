# ==============================================================================
#
# `是 STR的 fusion_v2.py` - 智能频率域融合模块 V2 (基于 SE Block)
#
# ==============================================================================

import torch
import torch.nn as nn
import torch.nn.functional as F
import pywt

# ==============================================================================
# 1. 小波变换辅助函数 (无改动)
# ==============================================================================

def create_2d_wavelet_filter(wave, in_size, out_size, type=torch.float):
    w = pywt.Wavelet(wave)
    dec_hi = torch.tensor(w.dec_hi[::-1], dtype=type)
    dec_lo = torch.tensor(w.dec_lo[::-1], dtype=type)
    dec_filters = torch.stack([dec_lo.unsqueeze(0) * dec_lo.unsqueeze(1),
                               dec_lo.unsqueeze(0) * dec_hi.unsqueeze(1),
                               dec_hi.unsqueeze(0) * dec_lo.unsqueeze(1),
                               dec_hi.unsqueeze(0) * dec_hi.unsqueeze(1)], dim=0)
    dec_filters = dec_filters[:, None].repeat(in_size, 1, 1, 1)

    rec_hi = torch.tensor(w.rec_hi, dtype=type)
    rec_lo = torch.tensor(w.rec_lo, dtype=type)
    rec_filters = torch.stack([rec_lo.unsqueeze(0) * rec_lo.unsqueeze(1),
                               rec_lo.unsqueeze(0) * rec_hi.unsqueeze(1),
                               rec_hi.unsqueeze(0) * rec_lo.unsqueeze(1),
                               rec_hi.unsqueeze(0) * rec_hi.unsqueeze(1)], dim=0)
    rec_filters = rec_filters[:, None].repeat(out_size, 1, 1, 1)
    return dec_filters, rec_filters

def wavelet_2d_transform(x, filters):
    b, c, h, w = x.shape
    pad = (filters.shape[2] - 2) // 2
    x = F.conv2d(x, filters, stride=2, groups=c, padding=pad)
    x = x.reshape(b, c, 4, h // 2, w // 2)
    return x

def inverse_2d_wavelet_transform(x, filters):
    b, c, _, h_half, w_half = x.shape
    pad = (filters.shape[2] - 2) // 2
    x = x.reshape(b, c * 4, h_half, w_half)
    x = F.conv_transpose2d(x, filters, stride=2, groups=c, padding=pad)
    return x

# ==============================================================================
# 2. 新的核心组件: ConvFusionBlock (先融合后加权)
# ==============================================================================
class MaxFusionBlock(nn.Module):
    """
    使用 "选择最大值" 规则进行特征融合
    """
    def __init__(self):
        super().__init__()

    def forward(self, feat_A, feat_B):
        return torch.max(feat_A, feat_B)


class SE_Block(nn.Module):
    def __init__(self, channel, is_dis=False):
        super(SE_Block, self).__init__()

        self.AvgPool = nn.AdaptiveAvgPool2d(1)
        self.FC = nn.Sequential(
            nn.Linear(channel, channel // 2, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channel // 2, channel, bias=False),
            nn.Sigmoid()
        )
        self.is_dis = is_dis

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.AvgPool(x).view(b, c)
        y = self.FC(y).view(b, c, 1, 1)
        out = x * y

        if self.is_dis is True:
            out = y

        return out
class ConvFusionBlock(nn.Module):
    """
    使用 "先卷积融合，后SE通道加权" 的混合策略
    """
    def __init__(self, in_channels, kernel_size=7):
        super().__init__()
        # 步骤 1: 初步融合层 (2C -> C)
        self.initial_fusion = nn.Sequential(
            nn.Conv2d(in_channels * 2, in_channels, kernel_size=kernel_size, padding=kernel_size//2, bias=False),
            nn.BatchNorm2d(in_channels),
            nn.LeakyReLU(0.1, inplace=True) # 使用 LeakyReLU
        )
        
        # 步骤 2: 使用您提供的 SE_Block 生成权重
        self.weight_generator = SE_Block(channel=in_channels, is_dis=True)


    def forward(self, feat_A, feat_B):
        # 拼接输入特征
        combined_feat = torch.cat([feat_A, feat_B], dim=1)
        
        # 步骤 1: 通过卷积层进行初步融合
        fused_representation = self.initial_fusion(combined_feat)
        
        # 步骤 2: 基于融合后的特征生成通道权重
        alpha_channel_weights = self.weight_generator(fused_representation)
        
        # 步骤 3: 使用学习到的通道权重对原始特征进行加权融合
        final_fused_feat = alpha_channel_weights * feat_A + (1 - alpha_channel_weights) * feat_B
        return final_fused_feat

# ==============================================================================
# 3. WTConvFusionV2 模块 - 使用 ConvFusionBlock 进行融合
# ==============================================================================
class WTConvFusion(nn.Module):
    def __init__(self, in_channels, wt_type='db1', levels=3, **kwargs):
        super().__init__()
        self.levels = levels
        self.wt_filter, self.iwt_filter = create_2d_wavelet_filter(wt_type, in_channels, in_channels, torch.float)
        self.wt_filter = nn.Parameter(self.wt_filter, requires_grad=False)
        self.iwt_filter = nn.Parameter(self.iwt_filter, requires_grad=False)
        
        # 空间分支 (保持不变)
        self.spatial_conv = nn.Sequential(
            nn.Conv2d(in_channels * 2, in_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
        
        # 智能融合组件 (替换为 ConvFusionBlock)
        # 低频分量融合模块
        self.fusion_ll = ConvFusionBlock(in_channels)
        
        # 高频分量融合模块 (独立权重)
        self.fusion_lh = MaxFusionBlock() # 水平细节
        self.fusion_hl = MaxFusionBlock() # 垂直细节
        self.fusion_hh = MaxFusionBlock() # 对角线细节
        
        self.frequency_scale = nn.Parameter(torch.ones(1, in_channels, 1, 1) * 0.1)

    def forward(self, feat_A, feat_B):
        # 1. 空间分支
        feat = torch.cat([feat_A, feat_B], dim=1)
        spatial_fused = self.spatial_conv(feat)

        # 2. 频率分支
        ll_A_in_levels, ll_B_in_levels = [], []
        h_A_in_levels, h_B_in_levels = [], []
        shapes_in_levels = []
        
        curr_ll_A, curr_ll_B = feat_A, feat_B
        
        # 分解阶段
        for i in range(self.levels):
            curr_shape = curr_ll_A.shape
            shapes_in_levels.append(curr_shape)
            
            if (curr_shape[2] % 2 > 0) or (curr_shape[3] % 2 > 0):
                pads = (0, curr_shape[3] % 2, 0, curr_shape[2] % 2)
                curr_ll_A = F.pad(curr_ll_A, pads)
                curr_ll_B = F.pad(curr_ll_B, pads)
            
            coeffs_A = wavelet_2d_transform(curr_ll_A, self.wt_filter)
            coeffs_B = wavelet_2d_transform(curr_ll_B, self.wt_filter)
            
            ll_A_in_levels.append(coeffs_A[:,:,0,:,:])
            ll_B_in_levels.append(coeffs_B[:,:,0,:,:])
            h_A_in_levels.append(coeffs_A[:,:,1:4,:,:])
            h_B_in_levels.append(coeffs_B[:,:,1:4,:,:])
            
            curr_ll_A, curr_ll_B = coeffs_A[:,:,0,:,:], coeffs_B[:,:,0,:,:]
        
        # 重构与融合阶段
        next_ll_fused = 0
        
        for i in range(self.levels - 1, -1, -1):
            curr_ll_A, curr_ll_B = ll_A_in_levels.pop(), ll_B_in_levels.pop()
            curr_h_A, curr_h_B = h_A_in_levels.pop(), h_B_in_levels.pop()
            curr_shape = shapes_in_levels.pop()
            
            # 融合低频分量
            fused_ll = self.fusion_ll(curr_ll_A, curr_ll_B)
            fused_ll = fused_ll + next_ll_fused
            
            # 独立融合三个高频分量
            fused_lh = self.fusion_lh(curr_h_A[:,:,0,:,:], curr_h_B[:,:,0,:,:])
            fused_hl = self.fusion_hl(curr_h_A[:,:,1,:,:], curr_h_B[:,:,1,:,:])
            fused_hh = self.fusion_hh(curr_h_A[:,:,2,:,:], curr_h_B[:,:,2,:,:])
            fused_h = torch.stack([fused_lh, fused_hl, fused_hh], dim=2)
            
            # 重构
            fused_coeffs = torch.cat([fused_ll.unsqueeze(2), fused_h], dim=2)
            next_ll_fused = inverse_2d_wavelet_transform(fused_coeffs, self.iwt_filter)
            next_ll_fused = next_ll_fused[:, :, :curr_shape[2], :curr_shape[3]]
        
        freq_fused = next_ll_fused
        
        # 3. 最终融合
        output = spatial_fused + freq_fused * self.frequency_scale
        return output

# ==============================================================================
# 4. 测试模块功能
# ==============================================================================
if __name__ == '__main__':
    in_channels = 64
    batch_size = 4
    height, width = 128, 128
    
    feat_A = torch.randn(batch_size, in_channels, height, width)
    feat_B = torch.randn(batch_size, in_channels, height, width)

    print("--- WTConvFusionV2 Module Test ---")
    wtconv_fusion_model = WTConvFusionV2(in_channels=in_channels, levels=2)
    wtconv_fusion_model.eval()

    with torch.no_grad():
        wt_output = wtconv_fusion_model(feat_A, feat_B)

    print(f"WTConvFusionV2 final fused output shape: {wt_output.shape}")
    total_params_wt = sum(p.numel() for p in wtconv_fusion_model.parameters() if p.requires_grad)
    print(f"Total trainable parameters in WTConvFusionV2 module: {total_params_wt:,}")
    assert wt_output.shape == (batch_size, in_channels, height, width)
    print("WTConvFusionV2 output shape is correct. Test passed.")
