## 构建 VCBNetCross - 带有特征交叉连接的VCBNet
from pyexpat import model
import torch
import torch.nn as nn
import torch.nn.functional as F
from functools import partial
from timm.models.layers import DropPath, to_2tuple, trunc_normal_
from timm.models.registry import register_model
from timm.models.vision_transformer import _cfg
import math

# 从原始VCBNet导入基础组件
from .vcbnet import van_base, DWConv, DoubleConv, Down, RegressionOutConv

class CrossUp(nn.Module):
    """带有交叉连接的上采样模块，用于U-Net分支"""
    def __init__(self, in_channels, out_channels, cross_channels, bilinear=True):
        super().__init__()
        # 如果是双线性插值，使用普通卷积减少通道数
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            # bilinear情况下，需要手动减少通道数
            self.up_conv = nn.Conv2d(in_channels, in_channels // 2, kernel_size=1)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
            self.up_conv = nn.Identity()
        
        # 处理交叉连接输入的1x1卷积，减少通道数
        self.cross_conv = nn.Conv2d(cross_channels, cross_channels // 4, kernel_size=1)
        
        # 计算连接后的通道数
        # skip连接: out_channels, 上采样: in_channels//2, 交叉连接: cross_channels//4
        combined_channels = out_channels + in_channels//2 + cross_channels // 4
        
        # 连接后的卷积处理
        self.conv = DoubleConv(combined_channels, out_channels)
        
    def forward(self, x1, x2, cross_x):
        # x1: [B, in_channels, H/n, W/n]
        # x2: [B, out_channels, H/n, W/n]
        # cross_x: [B, cross_channels, H/n, W/n]
        
        # 上采样
        x1 = self.up(x1)  # [B, in_channels, H/(n/2), W/(n/2)]
        x1 = self.up_conv(x1)  # [B, in_channels//2, H/(n/2), W/(n/2)]
        
        # 确保空间维度匹配
        if x2.size(2) != x1.size(2) or x2.size(3) != x1.size(3):
            x1 = F.interpolate(x1, size=(x2.size(2), x2.size(3)), mode='bilinear', align_corners=True)
            
        # 处理交叉连接的特征
        cross_x_processed = self.cross_conv(cross_x)  # [B, cross_channels//4, H/n, W/n]
        
        # 确保交叉连接特征的空间维度匹配
        if cross_x_processed.size(2) != x2.size(2) or cross_x_processed.size(3) != x2.size(3):
            cross_x_processed = F.interpolate(cross_x_processed, size=(x2.size(2), x2.size(3)), 
                                             mode='bilinear', align_corners=True)
        
        # 先连接x2和x1，与原始Up模块保持一致
        x = torch.cat([x2, x1], dim=1)  # [B, out_channels + in_channels//2, H/n, W/n]
        
        # 再连接交叉特征
        x = torch.cat([x, cross_x_processed], dim=1)  # [B, out_channels + in_channels//2 + cross_channels//4, H/n, W/n]
        
        return self.conv(x)  # [B, out_channels, H/n, W/n]

class VanUpBlock(nn.Module):
    """带有交叉连接的VAN分支上采样块"""
    def __init__(self, in_channels, out_channels, cross_channels):
        super().__init__()
        self.up = nn.Sequential(
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True),
            nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=True),
            nn.BatchNorm2d(out_channels),
            nn.GELU()
        )
        
        # 处理交叉连接输入
        self.cross_conv = nn.Conv2d(cross_channels, out_channels // 2, kernel_size=1)
        
        # 特征融合卷积
        self.fusion_conv = nn.Conv2d(out_channels + out_channels // 2, out_channels, kernel_size=1)
        
    def forward(self, x, cross_x):
        # x: [B, in_channels, H/n, W/n]
        # cross_x: [B, cross_channels, H/n, W/n]
        
        x = self.up(x)  # [B, out_channels, H/(n/2), W/(n/2)]
        
        # 处理交叉连接
        cross_x_processed = self.cross_conv(cross_x)  # [B, out_channels//2, H/n, W/n]
        
        # 确保空间维度匹配
        if cross_x_processed.size(2) != x.size(2) or cross_x_processed.size(3) != x.size(3):
            cross_x_processed = F.interpolate(cross_x_processed, size=(x.size(2), x.size(3)), 
                                             mode='bilinear', align_corners=True)
        
        # 连接并应用最终卷积
        x = torch.cat([x, cross_x_processed], dim=1)  # [B, out_channels + out_channels//2, H/(n/2), W/(n/2)]
        x = self.fusion_conv(x)  # [B, out_channels, H/(n/2), W/(n/2)]
        return x

class VCBNetCross(nn.Module):
    def __init__(self, n_channels=3, n_outputs=1, bilinear=True):
        super(VCBNetCross, self).__init__()
        self.n_channels = n_channels
        self.n_outputs = n_outputs  # 输出通道数，对于回归任务通常为1
        self.bilinear = bilinear
        
        # VAN骨干网络
        self.backbone = van_base(pretrained=True)
        
        # U-Net编码器部分
        self.conv = DoubleConv(n_channels, 32)
        self.down1_1 = Down(32, 64)
        self.down1_2 = Down(64, 128)
        self.down1_3 = Down(128, 256)
        self.down1_4 = Down(256, 512)
        
        # U-Net解码器部分（带交叉连接）
        self.up1_1 = CrossUp(512, 256, 320, bilinear)  # 从VAN的stage3接收特征 (stage3: [B, 320, H/8, W/8])
        self.up1_2 = CrossUp(256, 128, 128, bilinear)  # 从VAN的stage2接收特征 (stage2: [B, 128, H/4, W/4])
        self.up1_3 = CrossUp(128, 64, 64, bilinear)    # 从VAN的stage1接收特征 (stage1: [B, 64, H/2, W/2])
        self.up1_4 = CrossUp(64, 32, 32, bilinear)     # 从VAN的van_feat接收特征 (van_feat: [B, 32, H, W])
        
        # VAN分支的上采样路径（带交叉连接）
        self.up4 = VanUpBlock(512, 256, 256)  # 从U-Net的x1_4接收特征 (x1_4: [B, 256, H/8, W/8])
        self.up3 = VanUpBlock(256, 128, 128)  # 从U-Net的x1_3接收特征 (x1_3: [B, 128, H/4, W/4])
        self.up2 = VanUpBlock(128, 64, 64)    # 从U-Net的x1_2接收特征 (x1_2: [B, 64, H/2, W/2])
        self.up1 = VanUpBlock(64, 32, 32)     # 从U-Net的x1_1接收特征 (x1_1: [B, 32, H, W])

        # 用于连接VAN特征的1x1卷积
        self.up4_ = nn.Conv2d(576, 256, 1)  # 576 = 256 + 320 (stage3)
        self.up3_ = nn.Conv2d(256, 128, 1)  # 256 = 128 + 128 (stage2)
        self.up2_ = nn.Conv2d(128, 64, 1)   # 128 = 64 + 64 (stage1)
        
        # 回归输出层
        self.cls_reg = nn.Conv2d(32, n_outputs, 1)
        self.out = RegressionOutConv(32, n_outputs)
        
    def forward(self, x):
        # 输入: [B, 3, H, W]
        
        # VAN骨干网络分支
        xx = self.backbone(x)
        # 功能: 通过VAN骨干网络提取多尺度特征
        # 输入: x [B, 3, H, W]
        # 输出: xx (stage1, stage2, stage3, stage4)
        
        
        stage1, stage2, stage3, stage4 = xx
        # stage1: [B, 64, H/2, W/2] - VAN第1阶段特征，分辨率降低2倍
        # stage2: [B, 128, H/4, W/4] - VAN第2阶段特征，分辨率降低4倍
        # stage3: [B, 320, H/8, W/8] - VAN第3阶段特征，分辨率降低8倍
        # stage4: [B, 512, H/16, W/16] - VAN第4阶段特征，分辨率降低16倍
        
        
        # U-Net编码器路径
        x1_1 = self.conv(x)
        # 功能: 初始卷积，提取基础特征
        # 输入: x [B, 3, H, W]
        # 输出: x1_1 [B, 32, H, W]
        
        
        x1_2 = self.down1_1(x1_1)
        # 功能: 第1次下采样，MaxPool + DoubleConv
        # 输入: x1_1 [B, 32, H, W]
        # 输出: x1_2 [B, 64, H/2, W/2]
        
        
        x1_3 = self.down1_2(x1_2)
        # 功能: 第2次下采样，MaxPool + DoubleConv
        # 输入: x1_2 [B, 64, H/2, W/2]
        # 输出: x1_3 [B, 128, H/4, W/4]
        
        
        x1_4 = self.down1_3(x1_3)
        # 功能: 第3次下采样，MaxPool + DoubleConv
        # 输入: x1_3 [B, 128, H/4, W/4]
        # 输出: x1_4 [B, 256, H/8, W/8]
        
        
        x1_5 = self.down1_4(x1_4)
        # 功能: 第4次下采样，MaxPool + DoubleConv
        # 输入: x1_4 [B, 256, H/8, W/8]
        # 输出: x1_5 [B, 512, H/16, W/16]
        
        
        # VAN解码器路径（带交叉连接）
        up4 = self.up4(stage4, x1_4)
        # 功能: VAN第1层上采样，接收U-Net编码器特征x1_4作为交叉连接
        # 输入: stage4 [B, 512, H/16, W/16], x1_4 [B, 256, H/8, W/8]
        # 输出: up4 [B, 256, H/8, W/8]
        
        
        up4 = torch.cat([up4, stage3], dim=1)
        # 功能: 连接VAN上采样特征和VAN编码器跳跃连接
        # 输入: up4 [B, 256, H/8, W/8], stage3 [B, 320, H/8, W/8]
        # 输出: up4 [B, 576, H/8, W/8] (256+320)
        
        
        up4 = self.up4_(up4)
        # 功能: 1x1卷积降维
        # 输入: up4 [B, 576, H/8, W/8]
        # 输出: up4 [B, 256, H/8, W/8]
        
        
        up3 = self.up3(up4, x1_3)
        # 功能: VAN第2层上采样，接收U-Net编码器特征x1_3作为交叉连接
        # 输入: up4 [B, 256, H/8, W/8], x1_3 [B, 128, H/4, W/4]
        # 输出: up3 [B, 128, H/4, W/4]
        
        
        up3 = torch.cat([up3, stage2], dim=1)
        # 功能: 连接VAN上采样特征和VAN编码器跳跃连接
        # 输入: up3 [B, 128, H/4, W/4], stage2 [B, 128, H/4, W/4]
        # 输出: up3 [B, 256, H/4, W/4] (128+128)
        
        
        up3 = self.up3_(up3)
        # 功能: 1x1卷积降维
        # 输入: up3 [B, 256, H/4, W/4]
        # 输出: up3 [B, 128, H/4, W/4]
        
        
        up2 = self.up2(up3, x1_2)
        # 功能: VAN第3层上采样，接收U-Net编码器特征x1_2作为交叉连接
        # 输入: up3 [B, 128, H/4, W/4], x1_2 [B, 64, H/2, W/2]
        # 输出: up2 [B, 64, H/2, W/2]
        
        
        up2 = torch.cat([up2, stage1], dim=1)
        # 功能: 连接VAN上采样特征和VAN编码器跳跃连接
        # 输入: up2 [B, 64, H/2, W/2], stage1 [B, 64, H/2, W/2]
        # 输出: up2 [B, 128, H/2, W/2] (64+64)
        
        
        up2 = self.up2_(up2)
        # 功能: 1x1卷积降维
        # 输入: up2 [B, 128, H/2, W/2]
        # 输出: up2 [B, 64, H/2, W/2]
        
        
        van_feat = self.up1(up2, x1_1)
        # 功能: VAN第4层上采样，接收U-Net编码器特征x1_1作为交叉连接
        # 输入: up2 [B, 64, H/2, W/2], x1_1 [B, 32, H, W]
        # 输出: van_feat [B, 32, H, W]
        
        
        # U-Net解码器路径（带交叉连接）
        x1_6 = self.up1_1(x1_5, x1_4, stage3)
        # 功能: U-Net第1层上采样，接收VAN编码器特征stage3作为交叉连接
        # 输入: x1_5 [B, 512, H/16, W/16], x1_4 [B, 256, H/8, W/8], stage3 [B, 320, H/8, W/8]
        # 输出: x1_6 [B, 256, H/8, W/8]
        
        
        x1_7 = self.up1_2(x1_6, x1_3, stage2)
        # 功能: U-Net第2层上采样，接收VAN编码器特征stage2作为交叉连接
        # 输入: x1_6 [B, 256, H/8, W/8], x1_3 [B, 128, H/4, W/4], stage2 [B, 128, H/4, W/4]
        # 输出: x1_7 [B, 128, H/4, W/4]
        
        
        x1_8 = self.up1_3(x1_7, x1_2, stage1)
        # 功能: U-Net第3层上采样，接收VAN编码器特征stage1作为交叉连接
        # 输入: x1_7 [B, 128, H/4, W/4], x1_2 [B, 64, H/2, W/2], stage1 [B, 64, H/2, W/2]
        # 输出: x1_8 [B, 64, H/2, W/2]
        
        
        unet_feat = self.up1_4(x1_8, x1_1, van_feat)
        # 功能: U-Net第4层上采样，接收VAN解码器最终特征van_feat作为交叉连接
        # 输入: x1_8 [B, 64, H/2, W/2], x1_1 [B, 32, H, W], van_feat [B, 32, H, W]
        # 输出: unet_feat [B, 32, H, W]
        

        # 生成两个分支的输出
        out1 = self.out(unet_feat)
        # 功能: U-Net分支回归输出
        # 输入: unet_feat [B, 32, H, W]
        # 输出: out1 [B, 1, H, W]
        
        
        out2 = self.cls_reg(van_feat)
        # 功能: VAN分支回归输出
        # 输入: van_feat [B, 32, H, W]
        # 输出: out2 [B, 1, H, W]
        
        
        # 融合两个分支的输出
        out = out1 * 0.4 + out2 * 0.6
        # 功能: 加权融合两个分支的输出
        # 输入: out1 [B, 1, H, W], out2 [B, 1, H, W]
        # 输出: out [B, 1, H, W]
        
        
        return out

if __name__ == '__main__':
    model = VCBNetCross(n_channels=3, n_outputs=1)
    model.eval()
    
    # 创建测试输入
    test_input = torch.randn(1, 3, 768, 1536)
    
    print(f"输入张量形状: {test_input.shape}")
    
    # 前向传播测试
    with torch.no_grad():
       out = model(test_input)
    print(f"输出张量形状: {out.shape}")
        
    # 计算模型参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")