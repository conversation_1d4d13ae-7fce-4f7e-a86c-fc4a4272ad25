import torch
import torch.nn as nn
import torch.nn.functional as F



class L1Loss(nn.Module):
    def __init__(self):
        super(L1Loss, self).__init__()

    def forward(self, pred, obs):
        return F.l1_loss(pred, obs, reduction='mean')

class MSELoss(nn.Module):
    def __init__(self):
        super(MSELoss, self).__init__()

    def forward(self, pred, obs):
        return F.mse_loss(pred, obs, reduction='mean')

class WMSELoss(nn.Module):
    def __init__(self):
        super().__init__()

    def forward(self, y_pred, y_true):
        """
        Calculates Weighted Mean Squared Error Loss in PyTorch (channels-first).
        
        Args:
            y_true (torch.Tensor): True values, shape (B, C, H, W).
            y_pred (torch.Tensor): Predicted values, shape (B, C, H, W).

        Returns:
            torch.Tensor: Loss tensor, shape (B, H, W).
        """
        device = y_true.device  # 获取 y_true (或 y_pred) 的设备
        # 根据预测值与真实值的差异设置不同的权重
        weights = torch.where(y_pred >= y_true,
                              torch.tensor(1.0, device=device),  # 当预测大于真实值时，权重为 1
                              torch.where(y_true >= 0.5, torch.tensor(6.0, device=device), torch.tensor(3.0, device=device)))  # 其他情况权重为 2 或 3
        
        diff = torch.square(y_pred - y_true)  # 计算平方误差
        loss = torch.mean(weights * diff, dim=1)  # 对每个像素计算加权平方误差
        scalar_loss = torch.mean(loss)  # 对整个批次求平均损失
        return scalar_loss

class WMAELoss(nn.Module):
    def __init__(self):
        super().__init__()  # 调用 nn.Module 的构造函数

    #def forward(self, y_true, y_pred):
    def forward(self, y_pred, y_true):
        """
        Calculates Weighted Mean Absolute Error Loss in PyTorch (channels-first).

        Args:
            y_true (torch.Tensor): True values, shape (B, C, H, W).
            y_pred (torch.Tensor): Predicted values, shape (B, C, H, W).

        Returns:
            torch.Tensor: Loss tensor, shape (B, H, W).
        """
        device = y_true.device  # 获取 y_true (或 y_pred) 的设备
        weights = torch.where(y_pred >= y_true,
                              torch.tensor(1.0, device=device),  # 将常量张量移动到同一设备
                              torch.where(y_true >= 0.5, torch.tensor(6.0, device=device), torch.tensor(4.0, device=device))) # 将常量张量移动到同一设备
        diff = torch.abs(y_pred - y_true)
        loss = torch.mean(weights * diff, dim=1)
        scalar_loss = torch.mean(loss)
        return scalar_loss 

class HMSELoss(nn.Module):
    def __init__(self, w0=5.0, w1=4.0):
        super(HMSELoss, self).__init__()
        self.w0 = w0
        self.w1 = w1

    def forward(self, pred, target):
        """
        Calculates Weighted Mean Squared Error Loss (Hilburn et al., 2021).

        Args:
            pred (torch.Tensor): Predicted values, shape (B, 1, H, W).
            target (torch.Tensor): True values, shape (B, 1, H, W).

        Returns:
            torch.Tensor: Scalar loss value.
        """
        squared_error = (pred - target) ** 2
        weights = torch.clamp(torch.exp(self.w0 * torch.pow(target, self.w1)), max=1e6)
        weighted_loss = weights * squared_error
        loss = torch.mean(weighted_loss)  # 直接使用 torch.mean
        return loss

class HMAELoss(nn.Module):
    def __init__(self, w0=5.0, w1=4.0):
        super(HMAELoss, self).__init__()
        self.w0 = w0
        self.w1 = w1

    def forward(self, pred, target):
        """
        Calculates Weighted Mean Absolute Error Loss (Hilburn et al., 2021).

        Args:
            pred (torch.Tensor): Predicted values, shape (B, 1, H, W).
            target (torch.Tensor): True values, shape (B, 1, H, W).

        Returns:
            torch.Tensor: Scalar loss value.
        """
        absolute_error = torch.abs(pred - target)
        weights = torch.clamp(torch.exp(self.w0 * torch.pow(target, self.w1)), max=1e6)
        weighted_loss = weights * absolute_error
        loss = torch.mean(weighted_loss)  # 直接使用 torch.mean
        return loss

class CSILoss(nn.Module):
    """
    Combines HMAELoss (for regression on high-intensity values) and 
    differentiable CSI loss (for balanced event detection).
    """
    def __init__(self, thresholds, data_max, data_min, w0=5.0, w1=4.0, k=10.0, alpha=0.5):
        super().__init__()
        self.hmae_loss = HMAELoss(w0, w1)
        self.thresholds_normalized = [(t - data_min) / (data_max - data_min) for t in thresholds]
        self.k = k
        self.alpha = alpha
        self.epsilon = 1e-6
        print(f"Initialized HMAECSILoss with alpha: {self.alpha}, w0: {w0}, w1: {w1}")

    def forward(self, pred, target):
        # 1. HMAE component
        hmae_component = self.hmae_loss(pred, target)

        # 2. CSI component
        csi_losses = []
        for t_norm in self.thresholds_normalized:
            t_norm_tensor = torch.tensor(t_norm, device=pred.device, dtype=pred.dtype)
            pred_prob = torch.sigmoid(self.k * (pred - t_norm_tensor))
            target_binary = (target >= t_norm_tensor).float()

            hits = torch.sum(pred_prob * target_binary)
            misses = torch.sum((1 - pred_prob) * target_binary)
            false_alarms = torch.sum(pred_prob * (1 - target_binary))
            
            csi = hits / (hits + misses + false_alarms + self.epsilon)
            csi_losses.append(1.0 - csi)
        
        csi_component = torch.mean(torch.stack(csi_losses))

        # 3. Combine
        total_loss = self.alpha * hmae_component + (1.0 - self.alpha) * csi_component
        return total_loss

class PODLoss(nn.Module):
    """
    Combines HMAELoss (for regression on high-intensity values) and 
    differentiable POD loss (for maximizing detection rate).
    """
    def __init__(self, thresholds, data_max, data_min, w0=5.0, w1=4.0, k=10.0, alpha=0.5):
        super().__init__()
        self.hmae_loss = HMAELoss(w0, w1)
        self.thresholds_normalized = [(t - data_min) / (data_max - data_min) for t in thresholds]
        self.k = k
        self.alpha = alpha
        self.epsilon = 1e-6
        print(f"Initialized HMAEPODLoss with alpha: {self.alpha}, w0: {w0}, w1: {w1}")

    def forward(self, pred, target):
        # 1. HMAE component
        hmae_component = self.hmae_loss(pred, target)

        # 2. POD component
        pod_losses = []
        for t_norm in self.thresholds_normalized:
            t_norm_tensor = torch.tensor(t_norm, device=pred.device, dtype=pred.dtype)
            pred_prob = torch.sigmoid(self.k * (pred - t_norm_tensor))
            target_binary = (target >= t_norm_tensor).float()

            hits = torch.sum(pred_prob * target_binary)
            misses = torch.sum((1 - pred_prob) * target_binary)
            
            pod = hits / (hits + misses + self.epsilon)
            pod_losses.append(1.0 - pod)
        
        pod_component = torch.mean(torch.stack(pod_losses))

        # 3. Combine
        total_loss = self.alpha * hmae_component + (1.0 - self.alpha) * pod_component
        return total_loss


class MSE3Loss(nn.Module):
    def forward(self, y_pred, y_true):
        """
        实现了与之前版本完全相同的逻辑，但使用了对GPU更友好的“扁平化”结构，
        以获得更好的性能。
        """
        device = y_true.device
        
        # 1. 初始化一个基础权重张量，所有权重都为1.0
        weights = torch.full_like(y_true, 1.0, device=device)

        # 2. 逐步应用更强的惩罚，后面的会覆盖前面的
        # 对所有欠预测，权重设为4.0
        weights = torch.where(y_pred < y_true, 4.0, weights)
        
        # 对欠预测且真实值>=30的情况，权重覆盖为6.0
        weights = torch.where((y_pred < y_true) & (y_true >= 30/60), 6.0, weights)
        
        # 对漏报了40阈值的情况，权重覆盖为10.0 (最高优先级)
        weights = torch.where((y_true >= 40/60) & (y_pred < 40/60), 10.0, weights)
        
        # 计算最终损失
        return torch.mean(weights * torch.square(y_pred - y_true))

class AsymmetricHMSELoss(nn.Module):
    def __init__(self, hmse_w0=5.0, hmse_w1=4.0, underprediction_penalty=4.0):
        """
        一个结合了指数权重和不对称惩罚的损失函数。

        该损失函数通过三个机制协同工作，以实现复杂而精确的优化目标：
        1.  **强度权重 (`hmse_weights`)**: 聚焦强事件。通过一个指数函数，对真实值(target)
            越大的区域，施加越高的基础权重。这解决了“重要性”问题。
        2.  **不对称惩罚 (`underprediction_penalty`)**: 鼓励高估。对模型的预测值低于真实值
            （欠预测）的情况，施加一个额外的惩罚乘数。这解决了“方向性”问题，旨在降低漏报率。
        3.  **均方误差 (MSE)**: 保证准确性。作为损失的基础，它的二次方特性会严厉惩罚
            任何偏离真实值的预测，像一根“缰绳”一样，防止模型在被鼓励高估后偏离太远。

        Args:
            hmse_w0 (float): 强度权重公式的系数，可称为“放大器”。它决定了权重增长的最终量级，
                           即你愿意为最强事件的错误付出的最大代价。
            hmse_w1 (float): 强度权重公式的指数，可称为“聚焦旋钮”。它决定了权重增长的“启动时机”
                           和剧烈程度，即损失函数是“全面关注”还是“只关注顶尖事件”。
            underprediction_penalty (float): 对欠预测的额外惩罚系数。大于1.0的值会惩罚欠预测。
                                            例如，4.0意味着在相同条件下，欠预测的损失是高估的4倍。
        """
        super(AsymmetricHMSELoss, self).__init__()
        # 参数校验
        if underprediction_penalty < 1.0:
            raise ValueError("underprediction_penalty must be >= 1.0 to penalize underprediction.")
        
        self.hmse_w0 = hmse_w0
        self.hmse_w1 = hmse_w1
        self.underprediction_penalty = underprediction_penalty

    def forward(self, pred, target):
        """
        计算加权损失。

        Args:
            pred (torch.Tensor): 模型的连续预测值。
            target (torch.Tensor): 真实的标签值。

        Returns:
            torch.Tensor: 一个标量 (scalar) 损失值。
        """
        # 1. 计算基础的均方误差 (MSE)
        squared_error = (pred - target) ** 2

        # 2. 计算聚焦强降水的“强度权重”
        # 该权重基于真实值 `target` 的大小，`target` 越大，权重呈指数级增长。
        # 注意：这里的 `target` 应该是归一化到 [0, 1] 区间的值。
        hmse_weights = torch.exp(self.hmse_w0 * torch.pow(target, self.hmse_w1))
        
        # 为了训练稳定性，给权重设置一个上限，防止因极端target值导致梯度爆炸
        hmse_weights = torch.clamp(hmse_weights, max=1e6)

        # 3. 计算“不对称惩罚权重”
        # 如果 pred < target (欠预测)，则权重为 `underprediction_penalty`
        # 否则 (高估或准确预测)，权重为 1.0，不施加额外惩罚。
        asymmetry_weights = torch.where(pred < target, self.underprediction_penalty, 1.0)

        # 4. 结合两种权重，得到最终权重
        # 最终权重是上述两者的乘积。
        # 因此，欠预测一个强事件会受到“强度”和“不对称”的双重惩罚。
        final_weights = hmse_weights * asymmetry_weights
        
        # 5. 计算加权损失，并得到最终的平均损失
        # 使用 .detach() 将权重从计算图中分离，使其仅作为常数乘数。
        # 这是一种稳定训练的常用技巧，它简化了梯度流，让优化目标更清晰：
        # 模型的任务就是减小 `squared_error`，而权重只负责调整这项任务在不同区域的重要性。
        weighted_loss = final_weights.detach() * squared_error
        
        # 对批次中所有像素点的损失求平均值
        loss = torch.mean(weighted_loss)
        
        return loss




