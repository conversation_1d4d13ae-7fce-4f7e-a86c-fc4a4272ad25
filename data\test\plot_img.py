from osgeo import gdal
import numpy as np
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature
import cartopy.mpl.ticker as cticker
import cartopy.io.shapereader as shpreader
import matplotlib as mpl
from cartopy.mpl.gridliner import LATITUDE_FORMATTER, LONGITUDE_FORMATTER
import os
from datetime import datetime

# 设置字体
mpl.rcParams['font.family'] = 'Times New Roman'
mpl.use('Agg')  # 使用非交互式后端，适合保存图片

def gen_lat_lon(l_lon: float, r_lon: float, t_lat: float, b_lat: float, res: float):
    """生成经纬度网格"""
    return np.arange(l_lon, r_lon + res / 2, res), np.arange(t_lat, b_lat - res / 2, -1 * res)

def plot_radar_comparison(timestamp, save_dir):
    """绘制一行五列的雷达回波对比图"""
    # 生成经纬度网格
    lon1, lat1 = gen_lat_lon(106.12, 124, 42, 25.4, 0.04)
    lon, lat = np.meshgrid(lon1, lat1)
    
    # 定义数据路径
    real_data_path = f"/Storage01/ShareData/STR/test/{timestamp}_match.npy"
    model_paths = {
        "IRUNet": f"/Storage01/ShareData/STR/IRUNet/pred/{timestamp}_pred.npy",
        "IRMUNet": f"/Storage01/ShareData/STR/IRMUNet/pred/{timestamp}_pred.npy",
        "VISUNet": f"/Storage01/ShareData/STR/VISUNet/pred/{timestamp}_pred.npy",
        "VISMUNet": f"/Storage01/ShareData/STR/VISMUNet/pred/{timestamp}_pred.npy"
    }
    
    # 读取真实数据
    try:
        real_data = np.load(real_data_path)
        real_data = real_data[0] * 60
        real_data[real_data < 10] = np.nan
    except FileNotFoundError:
        print(f"真实数据文件不存在: {real_data_path}")
        return
    
    # 读取模型数据
    model_data = {}
    for model_name, path in model_paths.items():
        try:
            data = np.load(path)
            data = data[0][0] * 60  # 根据原始代码处理
            data[data < 10] = np.nan
            model_data[model_name] = data
        except FileNotFoundError:
            print(f"模型数据文件不存在: {path}")
            model_data[model_name] = np.full_like(real_data, np.nan)
    
    # 设置投影和地图边界
    proj = ccrs.Miller()
    
    # 读取省界和九段线
    province = shpreader.Reader('/Storage01/ShareData/shp/shp/bou2_4p.shp')
    line_nine = shpreader.Reader('/Storage01/ShareData/shp/nine/night_line.shp')
    
    # 创建图形
    fig = plt.figure(figsize=(25, 6), dpi=200)  # 宽比高大，适合1行5列
    
    # 定义色阶和颜色
    clevs = [i-0.000001 for i in range(-5, 80, 5)]
    cdict = ['#ffffff', "#bae9fe", '#4cc2fe', '#01a0f6', '#00ecec', '#00d800', '#019000',
             '#ffff00', '#e7c000', '#ff9000', '#ff0000', '#d60000', '#c00000', '#ff00f0',
             '#9600b4', '#010101']  # 自定义颜色列表
    
    # 创建子图
    titles = ["Actual", "IRUNet", "IRMUNet", "VISUNet", "VISMUNet"]
    data_list = [real_data] + [model_data[model] for model in model_paths.keys()]
    
    axes = []
    for i in range(5):
        ax = fig.add_subplot(1, 5, i+1, projection=proj)
        axes.append(ax)
        
        # 设置地图边界
        ax.set_extent([106.12, 124, 25.4, 42], crs=ccrs.PlateCarree())
        
        # 添加省界和九段线
        ax.add_geometries(province.geometries(), crs=ccrs.PlateCarree(), linewidth=.2, edgecolor='k', facecolor='none')
        ax.add_geometries(line_nine.geometries(), crs=ccrs.PlateCarree(), linewidth=.2, edgecolor='k', facecolor='none')
        
        # 添加标题
        ax.set_title(titles[i], loc='center', fontsize=12)
        
        # 绘制等值线填充
        cf = ax.contourf(lon, lat, data_list[i], levels=clevs, colors=cdict, transform=ccrs.PlateCarree())
        
        # 添加网格线
        gl = ax.gridlines(draw_labels=True, crs=ccrs.PlateCarree(), rotate_labels=0,
                        linewidth=0.2, linestyle='--',
                        xlocs=np.arange(105, 125, 5), ylocs=np.arange(25, 45, 5))
        
        # 只在第一个子图左侧显示纬度标签
        if i == 0:
            gl.ylabels_left = True
        else:
            gl.ylabels_left = False
        
        # 其他网格线设置
        gl.xlabels_top = False
        gl.ylabels_right = False
        gl.xformatter = LONGITUDE_FORMATTER
        gl.yformatter = LATITUDE_FORMATTER
        gl.xlabel_style = {"size": 8}
        gl.ylabel_style = {"size": 8}
    
    # 添加共享的颜色条
    cbar_ax = fig.add_axes([0.92, 0.15, 0.01, 0.7])  # 位置和大小可调整
    cbar = fig.colorbar(cf, cax=cbar_ax)
    cbar.ax.set_title('dBZ')
    
    # 设置整体标题
    fig.suptitle(f'Time: {timestamp} (UTC)', fontsize=14)
    
    # 调整布局
    plt.tight_layout(rect=[0, 0, 0.9, 0.95])  # 为colorbar和总标题留出空间
    
    # 创建保存目录（如果不存在）
    os.makedirs(save_dir, exist_ok=True)
    
    # 保存图像
    save_path = os.path.join(save_dir, f"{timestamp}.png")
    plt.savefig(save_path, bbox_inches='tight')
    plt.close()
    
    print(f"成功保存图像: {save_path}")

def main():
    # 设置保存目录
    save_dir = "/Storage01/ShareData/STR/plot"
    
    # 获取test目录中所有文件
    test_dir = "/Storage01/ShareData/STR/test"
    test_files = [f for f in os.listdir(test_dir) if f.endswith('_match.npy')]
    
    # 提取时间戳
    timestamps = [f.split('_')[0] for f in test_files]
    
    # 按时间排序
    timestamps.sort()
    
    # 处理每个时间戳
    for timestamp in timestamps:
        print(f"处理时间戳: {timestamp}")
        plot_radar_comparison(timestamp, save_dir)

if __name__ == "__main__":
    main()