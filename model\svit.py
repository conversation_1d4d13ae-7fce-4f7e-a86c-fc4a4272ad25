import os
import torch
import torch.nn as nn
from einops import rearrange
from einops.layers.torch import Rearrange
import time

######### helpers #########

def pair(t):
    return t if isinstance(t, tuple) else (t, t)


def posemb_sincos_2d(patches, temperature=10000, dtype=torch.float32):
    _, h, w, dim, device, dtype = *patches.shape, patches.device, patches.dtype

    y, x = torch.meshgrid(torch.arange(h, device=device),
                          torch.arange(w, device=device))
    assert (dim % 4) == 0, 'feature dimension must be multiple of 4 for sincos emb'
    omega = torch.arange(dim // 4, device=device) / (dim // 4 - 1)
    omega = 1. / (temperature ** omega)

    y = y.flatten()[:, None] * omega[None, :]
    x = x.flatten()[:, None] * omega[None, :]
    pe = torch.cat((x.sin(), x.cos(), y.sin(), y.cos()), dim=1)
    return pe.type(dtype)

######### classes #########


class FeedForward(nn.Module):
    def __init__(self, dim, hidden_dim, dropout=0.2):
        super().__init__()
        self.net = nn.Sequential(
            nn.LayerNorm(dim),
            nn.Linear(dim, hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, dim),
            nn.Dropout(dropout),
        )

    def forward(self, x):
        return self.net(x)


class Attention(nn.Module):
    def __init__(self, dim, heads=8, dim_head=64):
        super().__init__()
        inner_dim = dim_head * heads
        self.heads = heads
        self.scale = dim_head ** -0.5
        self.norm = nn.LayerNorm(dim)

        self.attend = nn.Softmax(dim=-1)

        self.to_qkv = nn.Linear(dim, inner_dim * 3, bias=False)
        self.to_out = nn.Linear(inner_dim, dim, bias=False)

    def forward(self, x):
        x = self.norm(x)

        qkv = self.to_qkv(x).chunk(3, dim=-1)
        q, k, v = map(lambda t: rearrange(
            t, 'b n (h d) -> b h n d', h=self.heads), qkv)

        dots = torch.matmul(q, k.transpose(-1, -2)) * self.scale

        attn = self.attend(dots)

        out = torch.matmul(attn, v)
        out = rearrange(out, 'b h n d -> b n (h d)')
        return self.to_out(out)


class Transformer(nn.Module):
    def __init__(self, dim, depth, heads, dim_head, mlp_dim):
        super().__init__()
        self.layers = nn.ModuleList([])
        for _ in range(depth):
            self.layers.append(nn.ModuleList([
                Attention(dim, heads=heads, dim_head=dim_head),
                FeedForward(dim, mlp_dim)
            ]))

    def forward(self, x):
        for attn, ff in self.layers:
            x = attn(x) + x
            x = ff(x) + x
        return x


class ViT(nn.Module):
    """
    Vision Transformer模型
    """

    def __init__(self, *, image_size, patch_size=(16, 16),
                 in_chans=4, out_chans=1, dim=512, depth=6, heads=12,
                 mlp_dim=512, dim_head=64):
        super().__init__()
        self.image_size = image_height, image_width = pair(image_size)
        self.patch_size = patch_height, patch_width = pair(patch_size)
        self.in_chans = in_chans
        self.out_chans = out_chans

        assert image_height % patch_height == 0 and image_width % patch_width == 0, 'Image dimensions must be divisible by the patch size.'

        self.num_patches = (image_height // patch_height) * \
            (image_width // patch_width)

        self.to_patch_embedding = nn.Sequential(
            nn.Conv2d(in_chans, dim, kernel_size=self.patch_size,
                      stride=self.patch_size),
            Rearrange('b d p1 p2 -> b p1 p2 d'),
            nn.LayerNorm(dim),
        )
        self.transformer = Transformer(dim, depth, heads, dim_head, mlp_dim)

        self.head = nn.Sequential(
            nn.LayerNorm(dim),
            nn.Linear(dim, out_chans*patch_height*patch_width, bias=False),
            Rearrange("b (h w) (c p1 p2) -> b c (h p1) (w p2)",
                      p1=patch_height,
                      p2=patch_width,
                      h=image_height // patch_height,
                      w=image_width // patch_width)
        )

    def forward(self, x):
        x = self.to_patch_embedding(x)  # b, p1, p2, d
        pe = posemb_sincos_2d(x)
        x = rearrange(x, 'b ... d -> b (...) d') + pe  # b, p1 * p2, d
        x = self.transformer(x)  # b, p1 * p2, d
        x = self.head(x)  # b, c, h, w
        return x


class Smoother(nn.Module):
    """
    Smoother模型，用于对ViT的输出进行平滑处理
    """
    def __init__(self, in_chans, out_chans, backbone=None, freezeweights=True, hiddens=None):
        super().__init__()
        self.backbone = backbone
        
        if freezeweights:
            self._freeze_weights()
        
        ni = in_chans
        
        if hiddens is None:
            # 默认隐藏层配置
            hiddens = [32, 16]
        
        if len(hiddens) > 0:
            assert isinstance(hiddens, list) and len(
                hiddens) > 0, 'Smoother: hiddens was found to be empty, but expected not to be'
            if not isinstance(hiddens[0], int):
                hiddens = [int(h) for h in hiddens]
            layers = []
            for c in hiddens:
                layers.append(nn.Conv2d(
                    ni, c, kernel_size=3, stride=1, padding=1, padding_mode='zeros'))
                layers.append(nn.ReLU())
                ni = c
            self.hiddens = nn.Sequential(*layers)

        self.head = nn.Conv2d(ni, out_chans, kernel_size=3,
                              stride=1, padding=1, padding_mode='zeros')

    def _freeze_weights(self):
        if self.backbone is not None:
            for param in self.backbone.parameters():
                param.requires_grad = False

    def forward(self, x):
        x = self.backbone(x)
        x = self.hiddens(x)
        x = self.head(x)
        return x


class SVit(nn.Module):
    """
    SVit模型，将ViT和Smoother组合在一起
    """
    def __init__(self, image_size, patch_size=(16, 16), in_chans=4, out_chans=1, 
                 dim=512, depth=6, heads=12, mlp_dim=512, dim_head=64,
                 freeze_vit=False, hidden_layers=None, load_pretrained=False, 
                 pretrained_path=None):
        super().__init__()
        
        # 创建ViT骨干网络
        self.vit = ViT(
            image_size=image_size,
            patch_size=patch_size,
            in_chans=in_chans,
            out_chans=1,  # ViT输出通道数固定为1
            dim=dim,
            depth=depth,
            heads=heads,
            mlp_dim=mlp_dim,
            dim_head=dim_head
        )
        
        # 加载预训练权重
        if load_pretrained and pretrained_path is not None:
            self._load_pretrained_weights(pretrained_path)
        
        # 创建Smoother
        self.smoother = Smoother(
            in_chans=1,  # ViT输出通道数
            out_chans=out_chans,
            backbone=self.vit,  # 将ViT作为backbone传递给Smoother
            freezeweights=freeze_vit,  # 是否冻结ViT权重
            hiddens=hidden_layers
        )
    
    def _load_pretrained_weights(self, pretrained_path):
        """
        加载预训练权重
        Args:
            pretrained_path: 预训练权重文件路径
        """
        try:
            print(f"[*] Loading pretrained weights from {pretrained_path}")
            
            # 检查文件是否存在
            if not os.path.exists(pretrained_path):
                print(f"[!] Pretrained weights file not found: {pretrained_path}")
                return
            
            # 加载检查点
            if torch.cuda.is_available():
                checkpoint = torch.load(pretrained_path)
            else:
                checkpoint = torch.load(pretrained_path, map_location='cpu')
            
            # 提取模型状态字典
            if 'model_state' in checkpoint:
                state_dict = checkpoint['model_state']
                print(f"[*] Loaded checkpoint from epoch {checkpoint.get('epoch', 'unknown')}")
                if 'best_val_metric' in checkpoint:
                    print(f"[*] Best validation metric: {checkpoint['best_val_metric']:.5f}")
            elif 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            else:
                state_dict = checkpoint
            
            # 处理分布式训练的权重键名（去除'module.'前缀）
            new_state_dict = {}
            for key, value in state_dict.items():
                if key.startswith('module.'):
                    new_key = key[7:]  # 去除'module.'前缀
                else:
                    new_key = key
                new_state_dict[new_key] = value
            
            # 加载权重到ViT模型
            missing_keys, unexpected_keys = self.vit.load_state_dict(new_state_dict, strict=False)
            
            if missing_keys:
                print(f"[!] Missing keys in pretrained weights: {missing_keys}")
            if unexpected_keys:
                print(f"[!] Unexpected keys in pretrained weights: {unexpected_keys}")
            
            print(f"[*] Successfully loaded pretrained weights")
            
        except Exception as e:
            print(f"[!] Error loading pretrained weights: {str(e)}")
            print(f"[!] Continuing with random initialization")
    
    def forward(self, x):
        # 由于backbone已经设置为self.vit，直接调用smoother即可
        return self.smoother(x)


def test_model(freeze_vit=False, load_pretrained=False, pretrained_path=None):
    """测试模型的不同配置
    
    实际有用的三种情况:
    1. 不冻结VIT，参加训练，但是没用预训练参数 (freeze_vit=False, load_pretrained=False)
    2. 不冻结VIT，参加训练，但是用预训练参数 (freeze_vit=False, load_pretrained=True)
    3. 冻结VIT，不参加训练，用预训练参数 (freeze_vit=True, load_pretrained=True)
    
    注意：冻结VIT且不使用预训练参数的组合没有实际意义，因此不推荐使用
    """
    # 检查无意义的组合
    if freeze_vit and not load_pretrained:
        print("\n警告: 冻结VIT但不使用预训练参数的组合没有实际意义！")
        print("推荐的组合:")
        print("1. freeze_vit=False, load_pretrained=False (从头训练)")
        print("2. freeze_vit=False, load_pretrained=True (微调全部参数)")
        print("3. freeze_vit=True, load_pretrained=True (冻结backbone微调)")
        return None
    
    print(f"\n=== 测试配置: freeze_vit={freeze_vit}, load_pretrained={load_pretrained} ===")
    
    # 创建测试输入
    batch_size = 1
    in_channels = 4
    height, width = 384, 384
    x = torch.randn(batch_size, in_channels, height, width)
    
    # 创建SVit模型
    svit_model = SVit(
        image_size=(height, width),
        patch_size=(16, 16),
        in_chans=in_channels,
        out_chans=1,
        dim=512,
        depth=4,
        heads=8,
        mlp_dim=64,
        dim_head=128,
        freeze_vit=freeze_vit,
        hidden_layers=[32, 16],
        load_pretrained=load_pretrained,
        pretrained_path=pretrained_path
    )
    
    # 统计参数
    total_params = sum(p.numel() for p in svit_model.parameters())
    trainable_params = sum(p.numel() for p in svit_model.parameters() if p.requires_grad)
    frozen_params = total_params - trainable_params
    
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数: {trainable_params:,}")
    print(f"冻结参数: {frozen_params:,}")
    
    # 测试前向传播
    with torch.no_grad():
        y = svit_model(x)
    
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {y.shape}")
    
    return svit_model


if __name__ == '__main__':
    # 预训练权重路径（根据实际情况修改）
    pretrained_path = "d:\\ZKY\\Code\\STR\\API\\complete01_vit_ckpt.pth"
    
    print("=" * 60)
    print("SVit 模型测试 - 三种实用配置")
    print("=" * 60)
    
    # 测试三种有实际意义的配置
    print("\n1. 不冻结VIT，不加载预训练权重（从头训练）")
    test_model(freeze_vit=False, load_pretrained=False)
    
    print("\n2. 不冻结VIT，加载预训练权重（全参数微调）")
    test_model(freeze_vit=False, load_pretrained=True, pretrained_path=pretrained_path)
    
    print("\n3. 冻结VIT，加载预训练权重（冻结backbone微调）")
    test_model(freeze_vit=True, load_pretrained=True, pretrained_path=pretrained_path)
    
    print("\n\n=== 演示无意义的组合 ===")
    print("\n4. 冻结VIT，不加载预训练权重（无实际意义）")
    test_model(freeze_vit=True, load_pretrained=False)