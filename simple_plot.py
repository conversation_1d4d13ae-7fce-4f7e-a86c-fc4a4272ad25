#!/usr/bin/env python3
"""
简化版绘图脚本 - 不依赖GDAL和cartopy
绘制预测结果的对比图
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import argparse
from pathlib import Path

# 设置字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['font.size'] = 10

def plot_simple_comparison(timestamp, save_dir, pred_dir="predictions", model_name="VCBNet"):
    """绘制简单的三列对比图：真实值、预测值、差值"""
    
    # 定义数据路径
    real_data_path = f"data/test/{timestamp}_match.npy"
    pred_data_path = f"{pred_dir}/{timestamp}_match_pred.npy"
    
    # 读取真实数据
    try:
        real_data = np.load(real_data_path)
        real_data = real_data[0] * 60  # 转换为dBZ
        real_data[real_data < 10] = np.nan  # 过滤小值
    except FileNotFoundError:
        print(f"真实数据文件不存在: {real_data_path}")
        return
    
    # 读取预测数据
    try:
        pred_data = np.load(pred_data_path)
        # 处理预测数据的维度
        if pred_data.ndim == 4:  # (batch, channel, height, width)
            pred_data = pred_data[0, 0] * 60
        elif pred_data.ndim == 3:  # (channel, height, width)
            pred_data = pred_data[0] * 60
        else:  # (height, width)
            pred_data = pred_data * 60
        pred_data[pred_data < 10] = np.nan  # 过滤小值
    except FileNotFoundError:
        print(f"预测数据文件不存在: {pred_data_path}")
        pred_data = np.full_like(real_data, np.nan)
    
    # 计算差值
    diff_data = pred_data - real_data
    
    # 创建图形
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 定义色阶和颜色
    clevs = np.arange(10, 80, 5)
    
    # 差值的色阶（对称的）
    diff_clevs = np.arange(-30, 35, 5)
    
    # 创建子图
    titles = ["Actual", f"{model_name} Prediction", "Difference (Pred - Actual)"]
    data_list = [real_data, pred_data, diff_data]
    
    for i in range(3):
        ax = axes[i]
        
        if i == 2:  # 差值图使用不同的色阶
            im = ax.imshow(data_list[i], cmap='RdBu_r', vmin=-30, vmax=30, 
                          origin='upper', aspect='auto')
            cbar = plt.colorbar(im, ax=ax, shrink=0.8)
            cbar.set_label('Difference (dBZ)', fontsize=10)
        else:  # 真实值和预测值使用相同色阶
            im = ax.imshow(data_list[i], cmap='jet', vmin=10, vmax=70, 
                          origin='upper', aspect='auto')
            cbar = plt.colorbar(im, ax=ax, shrink=0.8)
            cbar.set_label('dBZ', fontsize=10)
        
        ax.set_title(titles[i], fontsize=12)
        ax.set_xlabel('Longitude Index')
        ax.set_ylabel('Latitude Index')
    
    # 设置整体标题
    fig.suptitle(f'Time: {timestamp} (UTC) - {model_name} Model', fontsize=14)
    
    # 调整布局
    plt.tight_layout()
    
    # 创建保存目录（如果不存在）
    os.makedirs(save_dir, exist_ok=True)
    
    # 保存图像
    save_path = os.path.join(save_dir, f"{timestamp}_simple.png")
    plt.savefig(save_path, dpi=200, bbox_inches='tight')
    plt.close()
    
    print(f"成功保存图像: {save_path}")

def calculate_metrics(real_data, pred_data):
    """计算预测指标"""
    # 移除NaN值
    mask = ~(np.isnan(real_data) | np.isnan(pred_data))
    real_valid = real_data[mask]
    pred_valid = pred_data[mask]
    
    if len(real_valid) == 0:
        return {}
    
    # 计算指标
    mse = np.mean((real_valid - pred_valid) ** 2)
    mae = np.mean(np.abs(real_valid - pred_valid))
    rmse = np.sqrt(mse)
    
    # 计算相关系数
    correlation = np.corrcoef(real_valid, pred_valid)[0, 1]
    
    return {
        'MSE': mse,
        'MAE': mae,
        'RMSE': rmse,
        'Correlation': correlation,
        'Valid_Points': len(real_valid)
    }

def main():
    parser = argparse.ArgumentParser(description='简化版雷达回波对比图绘制')
    parser.add_argument('--save_dir', type=str, default='plots', help='图像保存目录')
    parser.add_argument('--pred_dir', type=str, default='predictions', help='预测结果目录')
    parser.add_argument('--test_dir', type=str, default='data/test', help='测试数据目录')
    parser.add_argument('--model_name', type=str, default='VCBNet', help='模型名称')
    parser.add_argument('--calculate_metrics', action='store_true', help='是否计算预测指标')
    
    args = parser.parse_args()
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 获取test目录中所有文件
    test_files = [f for f in os.listdir(args.test_dir) if f.endswith('_match.npy')]
    
    if not test_files:
        print(f"在 {args.test_dir} 中没有找到测试文件")
        return
    
    # 提取时间戳
    timestamps = [f.split('_')[0] for f in test_files]
    timestamps.sort()
    
    print(f"找到 {len(timestamps)} 个时间戳")
    
    # 用于存储所有指标
    all_metrics = []
    
    # 处理每个时间戳
    for timestamp in timestamps:
        print(f"处理时间戳: {timestamp}")
        plot_simple_comparison(timestamp, args.save_dir, args.pred_dir, args.model_name)
        
        # 如果需要计算指标
        if args.calculate_metrics:
            try:
                # 读取数据
                real_data_path = f"{args.test_dir}/{timestamp}_match.npy"
                pred_data_path = f"{args.pred_dir}/{timestamp}_match_pred.npy"
                
                real_data = np.load(real_data_path)[0] * 60
                pred_data = np.load(pred_data_path)
                if pred_data.ndim == 4:
                    pred_data = pred_data[0, 0] * 60
                elif pred_data.ndim == 3:
                    pred_data = pred_data[0] * 60
                else:
                    pred_data = pred_data * 60
                
                # 计算指标
                metrics = calculate_metrics(real_data, pred_data)
                metrics['Timestamp'] = timestamp
                all_metrics.append(metrics)
                
                print(f"  指标 - MSE: {metrics['MSE']:.4f}, MAE: {metrics['MAE']:.4f}, "
                      f"RMSE: {metrics['RMSE']:.4f}, Corr: {metrics['Correlation']:.4f}")
                
            except Exception as e:
                print(f"  计算指标时出错: {e}")
    
    # 如果计算了指标，输出平均值
    if args.calculate_metrics and all_metrics:
        avg_mse = np.mean([m['MSE'] for m in all_metrics])
        avg_mae = np.mean([m['MAE'] for m in all_metrics])
        avg_rmse = np.mean([m['RMSE'] for m in all_metrics])
        avg_corr = np.mean([m['Correlation'] for m in all_metrics])
        
        print(f"\n平均指标:")
        print(f"  MSE: {avg_mse:.4f}")
        print(f"  MAE: {avg_mae:.4f}")
        print(f"  RMSE: {avg_rmse:.4f}")
        print(f"  Correlation: {avg_corr:.4f}")
    
    print("所有图像生成完成！")

if __name__ == "__main__":
    main()
