#!/usr/bin/env python3
"""
简化版VCBNet特征可视化工具
专注解决用户的三个问题：
1. 通道数显示限制
2. VAN分支特征缺失
3. 结果解读困难
"""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from model import VCBNet
import json

class SimpleVCBNetVisualizer:
    def __init__(self, model_path, device='auto'):
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        self.model = self._load_model(model_path)
        self.features = {}
        
    def _load_model(self, model_path):
        """加载模型"""
        model_dir = os.path.dirname(os.path.dirname(model_path))
        config_path = os.path.join(model_dir, 'model_param.json')
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        model = VCBNet(n_channels=config['in_shape'][0], n_outputs=config['out_chans'])
        
        checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
        state_dict = checkpoint['model']
        
        new_state_dict = {}
        for key, value in state_dict.items():
            if key.startswith('_orig_mod.'):
                new_key = key[10:]
            else:
                new_key = key
            new_state_dict[new_key] = value
        
        model.load_state_dict(new_state_dict, strict=False)
        model.to(self.device)
        model.eval()
        
        return model
    
    def extract_features_manually(self, input_tensor):
        """手动提取特征，确保获取所有关键层"""
        self.features.clear()
        
        with torch.no_grad():
            x = input_tensor
            
            # === VAN分支 ===
            # 1. VAN编码器 (backbone)
            van_stages = self.model.backbone(x)
            stage1, stage2, stage3, stage4 = van_stages
            
            self.features['van_encode_stage1'] = stage1
            self.features['van_encode_stage2'] = stage2  
            self.features['van_encode_stage3'] = stage3
            self.features['van_encode_stage4'] = stage4
            
            # 2. VAN解码器
            up4 = self.model.up4(stage4)
            up4 = torch.cat([up4, stage3], dim=1)
            up4 = self.model.up4_(up4)
            self.features['van_decode_up4'] = up4
            
            up3 = self.model.up3(up4)
            up3 = torch.cat([up3, stage2], dim=1)
            up3 = self.model.up3_(up3)
            self.features['van_decode_up3'] = up3
            
            up2 = self.model.up2(up3)
            up2 = torch.cat([up2, stage1], dim=1)
            up2 = self.model.up2_(up2)
            self.features['van_decode_up2'] = up2
            
            van_final = self.model.up1(up2)
            self.features['van_decode_final'] = van_final
            
            # === U-Net分支 ===
            # 1. U-Net编码器
            x1_1 = self.model.conv(x)
            self.features['unet_encode_conv'] = x1_1
            
            x1_2 = self.model.down1_1(x1_1)
            self.features['unet_encode_down1'] = x1_2
            
            x1_3 = self.model.down1_2(x1_2)
            self.features['unet_encode_down2'] = x1_3
            
            x1_4 = self.model.down1_3(x1_3)
            self.features['unet_encode_down3'] = x1_4
            
            x1_5 = self.model.down1_4(x1_4)
            self.features['unet_encode_down4'] = x1_5
            
            # 2. U-Net解码器
            x1_6 = self.model.up1_1(x1_5, x1_4)
            self.features['unet_decode_up1'] = x1_6
            
            x1_7 = self.model.up1_2(x1_6, x1_3)
            self.features['unet_decode_up2'] = x1_7
            
            x1_8 = self.model.up1_3(x1_7, x1_2)
            self.features['unet_decode_up3'] = x1_8
            
            unet_final = self.model.up1_4(x1_8, x1_1)
            self.features['unet_decode_final'] = unet_final
        
        return self.features
    
    def create_comprehensive_channel_view(self, features, save_dir, sample_name, 
                                        channels_per_row=8, max_total_channels=32):
        """创建全面的通道视图，解决通道数显示限制问题"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 按类型分组特征
        feature_groups = {
            'VAN编码器': {k: v for k, v in features.items() if 'van_encode' in k},
            'VAN解码器': {k: v for k, v in features.items() if 'van_decode' in k},
            'UNet编码器': {k: v for k, v in features.items() if 'unet_encode' in k},
            'UNet解码器': {k: v for k, v in features.items() if 'unet_decode' in k}
        }
        
        for group_name, group_features in feature_groups.items():
            if not group_features:
                continue
                
            print(f"\n=== {group_name} ===")
            
            for layer_name, feature in group_features.items():
                if feature.dim() != 4:
                    continue
                    
                feat_np = feature[0].cpu().numpy()  # (C, H, W)
                total_channels = feat_np.shape[0]
                
                print(f"{layer_name}: {feat_np.shape} (显示前{min(total_channels, max_total_channels)}个通道)")
                
                # 限制显示的通道数
                show_channels = min(total_channels, max_total_channels)
                
                # 计算子图布局
                rows = (show_channels + channels_per_row - 1) // channels_per_row
                
                fig, axes = plt.subplots(rows, channels_per_row, 
                                       figsize=(2*channels_per_row, 2*rows))
                
                if rows == 1:
                    axes = axes.reshape(1, -1) if show_channels > 1 else [axes]
                elif show_channels == 1:
                    axes = [[axes]]
                
                for i in range(show_channels):
                    row = i // channels_per_row
                    col = i % channels_per_row
                    
                    if rows == 1:
                        ax = axes[col] if show_channels > 1 else axes
                    else:
                        ax = axes[row, col]
                    
                    im = ax.imshow(feat_np[i], cmap='viridis', aspect='auto')
                    ax.set_title(f'Ch{i}', fontsize=8)
                    ax.set_xticks([])
                    ax.set_yticks([])
                    
                    # 添加小的颜色条
                    plt.colorbar(im, ax=ax, shrink=0.8, pad=0.02)
                
                # 隐藏多余的子图
                for i in range(show_channels, rows * channels_per_row):
                    row = i // channels_per_row
                    col = i % channels_per_row
                    if rows == 1:
                        if show_channels > 1:
                            axes[col].axis('off')
                    else:
                        axes[row, col].axis('off')
                
                plt.suptitle(f'{group_name} - {layer_name}\n总通道数: {total_channels}, 显示: {show_channels}', 
                           fontsize=12)
                plt.tight_layout()
                
                # 保存
                safe_name = layer_name.replace('/', '_').replace('\\', '_')
                save_path = os.path.join(save_dir, f'{sample_name}_{safe_name}_channels.png')
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                plt.close()
                
                print(f"  保存: {save_path}")
    
    def create_branch_comparison_summary(self, features, save_dir, sample_name):
        """创建分支对比总结，帮助理解结果"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 提取对应层进行对比
        comparison_pairs = [
            ('van_decode_up4', 'unet_decode_up1', '最深层解码'),
            ('van_decode_up3', 'unet_decode_up2', '中深层解码'),
            ('van_decode_up2', 'unet_decode_up3', '中浅层解码'),
            ('van_decode_final', 'unet_decode_final', '最终输出层')
        ]
        
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        
        for i, (van_layer, unet_layer, layer_desc) in enumerate(comparison_pairs):
            if van_layer in features and unet_layer in features:
                van_feat = features[van_layer][0].cpu().numpy()
                unet_feat = features[unet_layer][0].cpu().numpy()
                
                # 计算平均激活图
                van_avg = np.mean(van_feat, axis=0)
                unet_avg = np.mean(unet_feat, axis=0)
                
                # 显示VAN分支
                im1 = axes[0, i].imshow(van_avg, cmap='hot', aspect='auto')
                axes[0, i].set_title(f'VAN\n{layer_desc}')
                axes[0, i].set_xticks([])
                axes[0, i].set_yticks([])
                plt.colorbar(im1, ax=axes[0, i], shrink=0.8)
                
                # 显示U-Net分支
                im2 = axes[1, i].imshow(unet_avg, cmap='hot', aspect='auto')
                axes[1, i].set_title(f'U-Net\n{layer_desc}')
                axes[1, i].set_xticks([])
                axes[1, i].set_yticks([])
                plt.colorbar(im2, ax=axes[1, i], shrink=0.8)
                
                # 计算相似度
                van_flat = van_avg.flatten()
                unet_flat = unet_avg.flatten()
                similarity = np.corrcoef(van_flat, unet_flat)[0, 1]
                
                # 在图上添加相似度信息
                axes[0, i].text(0.02, 0.98, f'相似度: {similarity:.3f}', 
                              transform=axes[0, i].transAxes, 
                              bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                              verticalalignment='top', fontsize=8)
        
        plt.suptitle(f'VCBNet双分支对比 - {sample_name}\n(显示各层平均激活模式)', fontsize=16)
        plt.tight_layout()
        
        save_path = os.path.join(save_dir, f'{sample_name}_branch_comparison_summary.png')
        plt.savefig(save_path, dpi=200, bbox_inches='tight')
        plt.close()
        
        print(f"保存分支对比总结: {save_path}")
    
    def create_easy_interpretation_guide(self, features, save_dir, sample_name):
        """创建简单易懂的解读指南"""
        
        guide = f"""# 🔍 VCBNet特征分析结果 - {sample_name}

## 📊 发现了什么特征？

### VAN分支 (基于注意力的分支)
"""
        
        van_features = {k: v for k, v in features.items() if 'van' in k}
        for name, feat in van_features.items():
            if feat.dim() == 4:
                feat_np = feat[0].cpu().numpy()
                channels = feat_np.shape[0]
                avg_activation = np.mean(feat_np)
                max_activation = np.max(feat_np)
                
                guide += f"- **{name}**: {channels}个通道, 平均激活={avg_activation:.3f}, 最大激活={max_activation:.3f}\n"
        
        guide += "\n### U-Net分支 (传统卷积分支)\n"
        
        unet_features = {k: v for k, v in features.items() if 'unet' in k}
        for name, feat in unet_features.items():
            if feat.dim() == 4:
                feat_np = feat[0].cpu().numpy()
                channels = feat_np.shape[0]
                avg_activation = np.mean(feat_np)
                max_activation = np.max(feat_np)
                
                guide += f"- **{name}**: {channels}个通道, 平均激活={avg_activation:.3f}, 最大激活={max_activation:.3f}\n"
        
        guide += f"""

## 🎯 如何看懂图片？

### 1. 通道图 (*_channels.png)
- **每个小图**: 代表一个特征通道
- **颜色**: 黄色=重要特征，蓝色=不重要区域
- **通道数**: 显示了前{min(32, max([f[0].shape[0] for f in features.values() if f.dim()==4]))}个通道（总共可能更多）

### 2. 分支对比图 (branch_comparison_summary.png)
- **上排**: VAN分支的平均激活模式
- **下排**: U-Net分支的平均激活模式  
- **相似度数字**: 越接近1表示两分支越相似，越接近0表示越互补

## 💡 这告诉我们什么？

### 如果相似度高 (>0.7):
- 两个分支学到了相似的特征
- 可能存在冗余，可以考虑简化模型

### 如果相似度低 (<0.3):
- 两个分支学到了不同的特征
- 这很好！说明模型充分利用了双分支结构
- 但可能需要更好的融合策略

### 如果某分支激活很弱:
- 该分支可能没有充分训练
- 或者该分支对当前样本不重要

## 🔧 改进建议

基于当前分析，建议关注：
1. 激活最强的层 - 这些是最重要的特征
2. 相似度最低的层 - 这些层最互补，融合策略最关键
3. 通道数最多的层 - 这些层计算量大，可能有优化空间

---
*分析完成时间: 自动生成*
"""
        
        guide_path = os.path.join(save_dir, f'{sample_name}_easy_guide.md')
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write(guide)
        
        print(f"保存简易指南: {guide_path}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='简化版VCBNet特征可视化')
    parser.add_argument('--model_path', type=str, default='721/vcbnet0/checkpoints/checkpoint.pth')
    parser.add_argument('--data_path', type=str, default='data/test/202210090536_match.npy')
    parser.add_argument('--output_dir', type=str, default='simple_feature_analysis')
    parser.add_argument('--max_channels', type=int, default=32, help='每层显示的最大通道数')
    parser.add_argument('--channels_per_row', type=int, default=8, help='每行显示的通道数')
    
    args = parser.parse_args()
    
    print("🚀 开始VCBNet特征分析...")
    print(f"模型: {args.model_path}")
    print(f"数据: {args.data_path}")
    print(f"最大通道数: {args.max_channels}")
    
    # 创建可视化器
    visualizer = SimpleVCBNetVisualizer(args.model_path)
    
    # 加载测试数据
    data = np.load(args.data_path)
    if data.ndim == 3:
        input_tensor = torch.from_numpy(data[:3]).unsqueeze(0).float()
    else:
        input_tensor = torch.from_numpy(data[:, :3]).float()
    
    input_tensor = input_tensor.to(visualizer.device)
    
    # 提取特征
    print("\n📊 提取特征中...")
    features = visualizer.extract_features_manually(input_tensor)
    
    sample_name = Path(args.data_path).stem
    
    print(f"✅ 成功提取 {len(features)} 个特征层:")
    for name, feat in features.items():
        if hasattr(feat, 'shape'):
            print(f"  {name}: {feat.shape}")
    
    # 生成可视化
    print("\n🎨 生成通道可视化...")
    visualizer.create_comprehensive_channel_view(
        features, args.output_dir, sample_name, 
        args.channels_per_row, args.max_channels
    )
    
    print("\n📈 生成分支对比...")
    visualizer.create_branch_comparison_summary(features, args.output_dir, sample_name)
    
    print("\n📝 生成解读指南...")
    visualizer.create_easy_interpretation_guide(features, args.output_dir, sample_name)
    
    print(f"\n🎉 分析完成！结果保存在: {args.output_dir}")
    print("\n📁 生成的文件:")
    print("- *_channels.png: 各层详细通道图")
    print("- *_branch_comparison_summary.png: 分支对比总结")
    print("- *_easy_guide.md: 简易解读指南")

if __name__ == "__main__":
    main()
