#!/usr/bin/env python3
"""
批量特征分析脚本
对多个测试样本进行VCBNet双分支特征分析
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from feature_visualizer import VCBNetFeatureVisualizer
import argparse

def analyze_multiple_samples(model_path, data_dir, output_dir, max_samples=5):
    """
    对多个样本进行特征分析
    
    Args:
        model_path: 模型路径
        data_dir: 测试数据目录
        output_dir: 输出目录
        max_samples: 最大分析样本数
    """
    
    # 创建可视化器
    visualizer = VCBNetFeatureVisualizer(model_path)
    
    # 获取测试文件
    test_files = list(Path(data_dir).glob('*_match.npy'))[:max_samples]
    
    print(f"找到 {len(test_files)} 个测试文件，将分析前 {len(test_files)} 个")
    
    all_similarities = []
    sample_names = []
    
    for i, test_file in enumerate(test_files):
        print(f"\n处理样本 {i+1}/{len(test_files)}: {test_file.name}")
        
        # 加载数据
        data = np.load(test_file)
        if data.ndim == 3:  # (C, H, W)
            input_tensor = torch.from_numpy(data[:3]).unsqueeze(0).float()
        else:  # (N, C, H, W)
            input_tensor = torch.from_numpy(data[:, :3]).float()
        
        input_tensor = input_tensor.to(visualizer.device)
        
        # 提取特征
        features = visualizer.extract_features(input_tensor)
        
        sample_name = test_file.stem
        sample_names.append(sample_name)
        sample_dir = os.path.join(output_dir, sample_name)
        
        # 生成各种可视化
        visualizer.visualize_feature_maps(features, sample_dir, sample_name, max_channels=8)
        visualizer.visualize_feature_statistics(features, sample_dir, sample_name)
        visualizer.visualize_spatial_attention(features, sample_dir, sample_name)
        similarities = visualizer.analyze_feature_similarity(features, sample_dir, sample_name)
        visualizer.generate_comprehensive_report(features, sample_dir, sample_name)
        
        all_similarities.append(similarities)
    
    # 生成跨样本对比分析
    generate_cross_sample_analysis(all_similarities, sample_names, output_dir)

def generate_cross_sample_analysis(all_similarities, sample_names, output_dir):
    """生成跨样本对比分析"""
    
    # 1. 相似性趋势分析
    plt.figure(figsize=(12, 8))
    
    layer_names = ['Layer 1', 'Layer 2', 'Layer 3', 'Layer 4']
    
    for i, (similarities, sample_name) in enumerate(zip(all_similarities, sample_names)):
        plt.plot(layer_names, similarities, 'o-', label=sample_name, linewidth=2, markersize=6)
    
    plt.xlabel('Decoder Layer')
    plt.ylabel('Feature Similarity')
    plt.title('Feature Similarity Trends Across Samples')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)
    
    plt.tight_layout()
    save_path = os.path.join(output_dir, 'cross_sample_similarity_trends.png')
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    # 2. 平均相似性分析
    if all_similarities:
        avg_similarities = np.mean(all_similarities, axis=0)
        std_similarities = np.std(all_similarities, axis=0)
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(layer_names, avg_similarities, yerr=std_similarities, 
                      capsize=5, color=['skyblue', 'lightcoral', 'lightgreen', 'gold'])
        
        plt.ylabel('Average Feature Similarity')
        plt.title('Average Feature Similarity Across All Samples')
        plt.ylim(0, 1)
        
        # 添加数值标签
        for bar, avg_sim, std_sim in zip(bars, avg_similarities, std_similarities):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std_sim + 0.02, 
                    f'{avg_sim:.3f}±{std_sim:.3f}', ha='center', va='bottom')
        
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        save_path = os.path.join(output_dir, 'average_similarity_analysis.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        # 3. 生成总结报告
        generate_summary_report(all_similarities, sample_names, avg_similarities, std_similarities, output_dir)

def generate_summary_report(all_similarities, sample_names, avg_similarities, std_similarities, output_dir):
    """生成总结报告"""
    
    report = f"""# VCBNet双分支特征分析总结报告

## 分析概览
- **分析样本数**: {len(sample_names)}
- **分析层数**: {len(avg_similarities)}
- **模型**: VCBNet (双分支结构)

## 样本列表
"""
    
    for i, sample_name in enumerate(sample_names):
        report += f"{i+1}. {sample_name}\n"
    
    report += f"""

## 平均特征相似性分析

| 解码器层 | 平均相似度 | 标准差 | 解释 |
|----------|------------|--------|------|
"""
    
    layer_names = ['Layer 1 (最深层)', 'Layer 2', 'Layer 3', 'Layer 4 (最浅层)']
    interpretations = [
        "高级语义特征，相似度高表明两分支学到相似的高级表示",
        "中级特征，相似度反映特征融合的互补性",
        "低级特征，相似度显示底层特征的一致性",
        "输出层特征，相似度影响最终预测的一致性"
    ]
    
    for i, (layer_name, avg_sim, std_sim, interpretation) in enumerate(zip(layer_names, avg_similarities, std_similarities, interpretations)):
        report += f"| {layer_name} | {avg_sim:.3f} | {std_sim:.3f} | {interpretation} |\n"
    
    # 分析结论
    max_sim_idx = np.argmax(avg_similarities)
    min_sim_idx = np.argmin(avg_similarities)
    
    report += f"""

## 关键发现

### 相似性模式
- **最高相似度层**: {layer_names[max_sim_idx]} ({avg_similarities[max_sim_idx]:.3f})
- **最低相似度层**: {layer_names[min_sim_idx]} ({avg_similarities[min_sim_idx]:.3f})
- **相似度范围**: {np.min(avg_similarities):.3f} - {np.max(avg_similarities):.3f}

### 稳定性分析
- **最稳定层** (最小标准差): {layer_names[np.argmin(std_similarities)]} (std: {np.min(std_similarities):.3f})
- **最不稳定层** (最大标准差): {layer_names[np.argmax(std_similarities)]} (std: {np.max(std_similarities):.3f})

## 改进建议

### 1. 融合策略优化
"""
    
    if avg_similarities[max_sim_idx] > 0.8:
        report += "- **高相似度层优化**: 最高相似度层可考虑参数共享或权重调整\n"
    
    if avg_similarities[min_sim_idx] < 0.5:
        report += "- **低相似度层优化**: 最低相似度层显示强互补性，可加强特征融合\n"
    
    if np.max(std_similarities) > 0.1:
        report += "- **稳定性改进**: 高标准差层需要提高跨样本一致性\n"
    
    report += f"""
### 2. 架构改进方向
- **自适应融合**: 根据层级相似度设计自适应权重
- **注意力机制**: 在低相似度层引入交叉注意力
- **特征对齐**: 对相似度差异大的层进行特征对齐

### 3. 训练策略
- **分支平衡**: 确保两分支训练平衡，避免某分支过拟合
- **正则化**: 对高相似度层添加多样性正则化
- **渐进训练**: 先训练单分支，再联合优化

## 文件结构
```
{output_dir}/
├── cross_sample_similarity_trends.png    # 跨样本相似性趋势
├── average_similarity_analysis.png       # 平均相似性分析
├── summary_report.md                      # 本报告
└── [sample_name]/                         # 各样本详细分析
    ├── *_channels.png                     # 特征图可视化
    ├── *_branch_comparison.png            # 分支对比
    ├── *_spatial_attention.png            # 空间注意力
    ├── *_feature_similarity.png           # 相似性分析
    └── *_analysis_report.md               # 样本分析报告
```

---
*报告生成时间: 自动生成*
"""
    
    # 保存报告
    report_path = os.path.join(output_dir, 'summary_report.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n总结报告保存至: {report_path}")

def main():
    parser = argparse.ArgumentParser(description='VCBNet批量特征分析')
    parser.add_argument('--model_path', type=str, default='721/vcbnet0/checkpoints/checkpoint.pth',
                       help='模型checkpoint路径')
    parser.add_argument('--data_dir', type=str, default='data/test',
                       help='测试数据目录')
    parser.add_argument('--output_dir', type=str, default='batch_feature_analysis',
                       help='输出目录')
    parser.add_argument('--max_samples', type=int, default=5,
                       help='最大分析样本数')
    
    args = parser.parse_args()
    
    print("开始批量特征分析...")
    print(f"模型路径: {args.model_path}")
    print(f"数据目录: {args.data_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"最大样本数: {args.max_samples}")
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 执行批量分析
    analyze_multiple_samples(args.model_path, args.data_dir, args.output_dir, args.max_samples)
    
    print(f"\n批量分析完成！结果保存在: {args.output_dir}")

if __name__ == "__main__":
    import torch  # 添加缺失的导入
    main()
