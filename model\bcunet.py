import torch
import torch.nn as nn
import torch.nn.functional as F
from functools import partial

# 实现timm.models.layers中的trunc_normal_和DropPath
def trunc_normal_(tensor, mean=0., std=1., a=-2., b=2.):
    # 截断正态分布初始化
    def norm_cdf(x):
        # 标准正态分布的累积分布函数近似
        return (1. + torch.erf(x / torch.sqrt(torch.tensor(2.)))) / 2.
    
    with torch.no_grad():
        l = norm_cdf((a - mean) / std)
        u = norm_cdf((b - mean) / std)
        tensor.uniform_(2 * l - 1, 2 * u - 1)
        tensor.erfinv_()
        tensor.mul_(std * torch.sqrt(torch.tensor(2.)))
        tensor.add_(mean)
        tensor.clamp_(min=a, max=b)
    return tensor

class DropPath(nn.Module):
    """Drop paths (Stochastic Depth) per sample (when applied in main path of residual blocks)."""
    def __init__(self, drop_prob=0.):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        # 处理shape=(B, 1, 1, 1)的掩码，以便它可以与任何形状的张量一起使用
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()  # 二值化掩码
        output = x.div(keep_prob) * random_tensor
        return output

# LayerNorm实现
class LayerNorm(nn.Module):
    """LayerNorm that supports two data formats: channels_last (default) or channels_first.
    The ordering of the dimensions in the inputs. channels_last corresponds to inputs with
    shape (batch_size, height, width, channels) while channels_first corresponds to inputs
    with shape (batch_size, channels, height, width).
    """
    def __init__(self, normalized_shape, eps=1e-6, data_format="channels_last"):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
        self.eps = eps
        self.data_format = data_format
        if self.data_format not in ["channels_last", "channels_first"]:
            raise NotImplementedError
        self.normalized_shape = (normalized_shape,)

    def forward(self, x):
        if self.data_format == "channels_last":
            return F.layer_norm(x, self.normalized_shape, self.weight, self.bias, self.eps)
        elif self.data_format == "channels_first":
            u = x.mean(1, keepdim=True)
            s = (x - u).pow(2).mean(1, keepdim=True)
            x = (x - u) / torch.sqrt(s + self.eps)
            x = self.weight[:, None, None] * x + self.bias[:, None, None]
            return x

# ConvNeXt Block实现
class Block(nn.Module):
    """ConvNeXt Block"""
    def __init__(self, dim, drop_path=0., layer_scale_init_value=1e-6):
        super().__init__()
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim)  # depthwise conv
        self.norm = LayerNorm(dim, eps=1e-6)
        self.pwconv1 = nn.Linear(dim, 4 * dim)  # pointwise/1x1 convs, implemented with linear layers
        self.act = nn.GELU()
        self.pwconv2 = nn.Linear(4 * dim, dim)
        self.gamma = nn.Parameter(layer_scale_init_value * torch.ones((dim)),
                                  requires_grad=True) if layer_scale_init_value > 0 else None
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        input = x
        x = self.dwconv(x)
        x = x.permute(0, 2, 3, 1)  # (N, C, H, W) -> (N, H, W, C)
        x = self.norm(x)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.pwconv2(x)
        if self.gamma is not None:
            x = self.gamma * x
        x = x.permute(0, 3, 1, 2)  # (N, H, W, C) -> (N, C, H, W)
        x = input + self.drop_path(x)
        return x

# ConvNeXt模型实现
class ConvNeXt(nn.Module):
    """ConvNeXt模型"""
    def __init__(self, in_chans=3, num_classes=1000,
                 depths=[3, 3, 9, 3], dims=[96, 192, 384, 768], drop_path_rate=0.3,
                 layer_scale_init_value=1e-6, head_init_scale=1., out_indices=[0,1,2,3]):
        super().__init__()
        self.out_indices = out_indices

        # 添加norm层
        norm_layer = partial(LayerNorm, eps=1e-6, data_format="channels_first")
        for i_layer in range(4):
            layer = norm_layer(dims[i_layer])
            layer_name = f'norm{i_layer}'
            self.add_module(layer_name, layer)

        # 下采样层
        self.downsample_layers = nn.ModuleList()
        stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=4, stride=2, padding=1),
            LayerNorm(dims[0], eps=1e-6, data_format="channels_first")
        )
        self.downsample_layers.append(stem)
        for i in range(3):
            downsample_layer = nn.Sequential(
                LayerNorm(dims[i], eps=1e-6, data_format="channels_first"),
                nn.Conv2d(dims[i], dims[i + 1], kernel_size=2, stride=2),
            )
            self.downsample_layers.append(downsample_layer)

        # 各阶段的Block
        self.stages = nn.ModuleList()
        dp_rates = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        cur = 0
        for i in range(4):
            stage = nn.Sequential(
                *[Block(dim=dims[i], drop_path=dp_rates[cur + j],
                        layer_scale_init_value=layer_scale_init_value) for j in range(depths[i])]
            )
            self.stages.append(stage)
            cur += depths[i]

        self.norm = nn.LayerNorm(dims[-1], eps=1e-6)  # final norm layer

    def _init_weights(self, m):
        if isinstance(m, (nn.Conv2d, nn.Linear)):
            trunc_normal_(m.weight, std=.02)
            nn.init.constant_(m.bias, 0)

    def forward_features(self, x):
        outs = []
        for i in range(4):
            x = self.downsample_layers[i](x)
            x = self.stages[i](x)
            if i in self.out_indices:
                norm_layer = getattr(self, f'norm{i}')
                x_out = norm_layer(x)
                outs.append(x_out)
        return tuple(outs)

    def forward(self, x):
        x = self.forward_features(x)
        return x

# 预训练模型URL
model_urls = {
    "convnext_tiny_1k": "https://dl.fbaipublicfiles.com/convnext/convnext_tiny_1k_224_ema.pth",
    "convnext_small_1k": "https://dl.fbaipublicfiles.com/convnext/convnext_small_1k_224_ema.pth",
    "convnext_base_1k": 'https://dl.fbaipublicfiles.com/convnext/convnext_base_1k_224_ema.pth',
    "convnext_large_1k": "https://dl.fbaipublicfiles.com/convnext/convnext_large_1k_224_ema.pth",
    "convnext_base_22k": "https://dl.fbaipublicfiles.com/convnext/convnext_base_22k_224.pth",
    "convnext_large_22k": "https://dl.fbaipublicfiles.com/convnext/convnext_large_22k_224.pth",
    "convnext_xlarge_22k": "https://dl.fbaipublicfiles.com/convnext/convnext_xlarge_22k_224.pth",
}

# convnext_base函数
def convnext_base(pretrained=False, in_22k=False, **kwargs):
    model = ConvNeXt(depths=[3, 3, 27, 3], dims=[128, 256, 512, 1024], **kwargs)
    if pretrained:
        url = model_urls['convnext_base_22k'] if in_22k else model_urls['convnext_base_1k']
        checkpoint = torch.hub.load_state_dict_from_url(url=url, map_location="cpu", check_hash=True)
        # 1. filter out unnecessary keys
        model_dict = model.state_dict()
        pretrained_dict = {k: v for k, v in checkpoint["model"].items() if k in model_dict}
        # 2. overwrite entries in the existing state dict
        model_dict.update(pretrained_dict)
        model.load_state_dict(model_dict)
    return model

# U-Net部分实现
class DoubleConv(nn.Module):
    """(convolution => [BN] => ReLU) * 2"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        return self.double_conv(x)

class Down(nn.Module):
    """Downscaling with maxpool then double conv"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.relu = nn.ReLU(inplace=True)
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            DoubleConv(in_channels, out_channels),
            nn.BatchNorm2d(out_channels)
        )
        self.max = nn.MaxPool2d(2)
        
    def forward(self, x1):
        x = self.maxpool_conv(x1)
        return self.relu(x)

class Up(nn.Module):
    """Upscaling then double conv"""
    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()

        # if bilinear, use the normal convolutions to reduce the number of channels
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)

        self.conv = DoubleConv(in_channels, out_channels)
        
    def forward(self, x1, x2):
        x1 = self.up(x1)
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

def conv1x1(in_channels, out_channels, groups=1):
    return nn.Sequential(
        nn.Conv2d(in_channels, out_channels, kernel_size=1, groups=groups, stride=1),
        nn.BatchNorm2d(out_channels)
    )

class RegressionOutConv(nn.Module):
    """回归输出层，替代原来的分割输出层"""
    def __init__(self, in_channels, out_channels):
        super(RegressionOutConv, self).__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(in_channels // 2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // 2, out_channels, kernel_size=1)
            # 注意：这里没有使用Sigmoid激活，因为是回归任务
        )

    def forward(self, x):
        return self.conv(x)

# BCU-Net回归模型
class BCUNet(nn.Module):
    """BCU-Net的回归版本，用于卫星降水反演任务"""
    def __init__(self, n_channels=3, n_outputs=1, bilinear=False):
        super(BCUNet, self).__init__()
        self.n_channels = n_channels
        self.n_outputs = n_outputs  # 输出通道数，对于回归任务通常为1
        self.backbone = convnext_base(pretrained=True, in_22k=True)   # pretrained=True 即使用预训练参数
        self.bilinear = bilinear
        self.pool = nn.MaxPool2d((2, 2))
        self.conv = DoubleConv(n_channels, 32)
        self.down1_1 = Down(32, 64)
        self.down1_2 = Down(64, 128)
        self.down1_3 = Down(128, 256)
        self.down1_4 = Down(256, 512)

        self.up1_1 = Up(512, 256, bilinear)
        self.up1_2 = Up(256, 128, bilinear)
        self.up1_3 = Up(128, 64, bilinear)
        self.up1_4 = Up(64, 32, bilinear)

        # ConvNeXt分支的上采样路径
        self.up1 = nn.Sequential(
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True),
            nn.Conv2d(128, 64, kernel_size=3, stride=1, padding=1, bias=True),
            nn.BatchNorm2d(64),
            nn.GELU()
        )
        self.up1_ = nn.Conv2d(128, 64, 1)

        self.up2 = nn.Sequential(
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True),
            nn.Conv2d(256, 128, kernel_size=3, stride=1, padding=1, bias=True),
            nn.BatchNorm2d(128),
            nn.GELU()
        )
        self.up2_ = nn.Conv2d(256, 128, 1)

        self.up3 = nn.Sequential(
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True),
            nn.Conv2d(512, 256, kernel_size=3, stride=1, padding=1, bias=True),
            nn.BatchNorm2d(256),
            nn.GELU()
        )
        self.up3_ = nn.Conv2d(512, 256, 1)

        self.up4 = nn.Sequential(
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True),
            nn.Conv2d(1024, 512, kernel_size=3, stride=1, padding=1, bias=True),
            nn.BatchNorm2d(512),
            nn.GELU()
        )
        self.up4_ = nn.Conv2d(1024, 512, 1)

        # 将输出层改为回归输出
        self.cls_reg = nn.Conv2d(64, n_outputs, 1)
        self.out = RegressionOutConv(32, n_outputs)

    def forward(self, x):
        # ConvNeXt骨干网络分支
        xx = self.backbone(x)
        stage1, stage2, stage3, stage4 = xx
        
        up4 = self.up4(stage4)
        up4 = torch.cat([up4, stage3], dim=1)
        up4 = self.up4_(up4)

        up3 = self.up3(up4)
        up3 = torch.cat([up3, stage2], dim=1)
        up3 = self.up3_(up3)

        up2 = self.up2(up3)
        up2 = torch.cat([up2, stage1], dim=1)
        up2 = self.up2_(up2)

        out = self.up1(up2)
        out2 = self.cls_reg(out)  # ConvNeXt分支输出

        # U-Net分支
        x1_1 = self.conv(x)
        x1_2 = self.down1_1(x1_1)
        x1_3 = self.down1_2(x1_2)
        x1_4 = self.down1_3(x1_3)
        x1_5 = self.down1_4(x1_4)
        x1_6 = self.up1_1(x1_5, x1_4)
        x1_7 = self.up1_2(x1_6, x1_3)
        x1_8 = self.up1_3(x1_7, x1_2)
        x1_9 = self.up1_4(x1_8, x1_1)
        out1 = self.out(x1_9)  # U-Net分支输出

        # 融合两个分支的输出
        out = out1 * 0.4 + out2 * 0.6
        
        return out


if __name__ == '__main__':
    import torch
    
    # 创建BCUNetRegression模型实例，使用预训练参数
    model = BCUNet(n_channels=3, n_outputs=1)  # 3通道输入，1通道输出（降水量）
    model.eval()
    
    # 创建测试输入 (batch_size=1, channels=3, height=512, width=512)
    test_input = torch.randn(1, 3, 768, 1536)
    
    print(f"输入张量形状: {test_input.shape}")
    
    # 前向传播测试
    with torch.no_grad():
        out = model(test_input)
    
    print(f"融合输出形状: {out.shape}")
    print("BCUNetRegression模型测试成功！")
    
    # 计算模型参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")