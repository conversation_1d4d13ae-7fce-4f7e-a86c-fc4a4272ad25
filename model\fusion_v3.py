import torch
import torch.nn as nn
import torch.nn.functional as F

class GatedSpatialChannelSelection(nn.Module):
    """
    A module that extends VCBNet2's spatial gating with channel selection.
    It performs complementary spatial selection and competitive channel selection.
    """
    def __init__(self, in_channels, reduction=16):
        super(GatedSpatialChannelSelection, self).__init__()
        self.in_channels = in_channels

        # --- Shared Fusion Network ---
        # A single, shared network to fuse the combined features efficiently.
        self.shared_fusion_network = nn.Sequential(
            nn.Conv2d(in_channels * 2, in_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )

        # --- Spatial Gating Branch ---
        # This branch now takes the FUSED feature map as input.
        self.spatial_head = nn.Sequential(
            nn.Conv2d(in_channels, 1, kernel_size=1),
            nn.Sigmoid()
        )
        
        # --- Channel Selection Branch ---
        # This branch also takes the FUSED feature map as input.
        self.channel_gap = nn.AdaptiveAvgPool2d(1)
        d = max(in_channels // reduction, 8)
        self.channel_fc1 = nn.Linear(in_channels, d)
        self.channel_fc2 = nn.Linear(d, in_channels) # Output one vector for sigmoid
        
        self.sigmoid = nn.Sigmoid()
        self.relu = nn.ReLU(inplace=True)

    def forward(self, unet_feat, van_feat):
        # unet_feat: (B, C, H, W)
        # van_feat: (B, C, H, W)
        
        combined_feat = torch.cat([unet_feat, van_feat], dim=1)

        # --- 1. Shared Fusion ---
        # Process the combined features ONCE through the shared fusion network.
        fused_feat_map = self.shared_fusion_network(combined_feat)

        # --- 2. Generate Spatial Gate (S) from the fused map ---
        s = self.spatial_head(fused_feat_map) # (B, 1, H, W)

        # --- 3. Generate Channel Selection Vectors (C1, C2) from the fused map ---
        gap = self.channel_gap(fused_feat_map).view(fused_feat_map.size(0), -1)
        z = self.relu(self.channel_fc1(gap))
        c1_vector = self.sigmoid(self.channel_fc2(z))
        
        # Reshape C1 for broadcasting and calculate C2 complementarily
        c1 = c1_vector.unsqueeze(-1).unsqueeze(-1) # (B, C, 1, 1)
        c2 = 1 - c1 # (B, C, 1, 1)

        # --- 3. Apply Combined Selection and Fuse ---
        # First, apply spatial gating (S and 1-S)
        # Then, apply channel selection (C1 and C2) to the spatially-weighted features
        
        w1 = s * unet_feat
        w2 = (1 - s) * van_feat
        
        w1_selected = w1 * c1
        w2_selected = w2 * c2
        
        # Final fusion by element-wise addition
        fused_feat = w1_selected + w2_selected
        
        return fused_feat

if __name__ == '__main__':
    # --- Test the GatedSpatialChannelSelection module ---
    in_channels = 32
    batch_size = 4
    height, width = 64, 64
    
    # Create dummy input tensors
    unet_feat = torch.randn(batch_size, in_channels, height, width)
    van_feat = torch.randn(batch_size, in_channels, height, width)
    
    print(f"--- Testing GatedSpatialChannelSelection ---")
    print(f"Input feature shape: {unet_feat.shape}")
    
    # Instantiate the module
    model = GatedSpatialChannelSelection(in_channels=in_channels)
    model.eval()
    
    # Forward pass
    with torch.no_grad():
        output = model(unet_feat, van_feat)
        
    print(f"Output feature shape: {output.shape}")
    
    # Check if the output shape is correct
    assert output.shape == (batch_size, in_channels, height, width)
    print("Output shape is correct.")
    
    # Calculate and print trainable parameters
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Trainable parameters in the module: {total_params:,}")
