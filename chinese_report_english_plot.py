#!/usr/bin/env python3
"""
VCBNet综合特征分析工具 - 中文报告+英文绘图版本
报告使用中文，绘图标题使用英文（避免字体问题）
"""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from simple_feature_visualizer import SimpleVCBNetVisualizer
import json
from datetime import datetime

class ChineseReportEnglishPlotAnalyzer:
    def __init__(self, model_path, device='auto'):
        self.visualizer = SimpleVCBNetVisualizer(model_path, device)
        self.all_features = {}
        self.sample_names = []
        
    def analyze_multiple_samples(self, data_paths, output_dir):
        """分析多个样本"""
        os.makedirs(output_dir, exist_ok=True)
        
        print("🔍 开始综合分析...")
        
        # 分析每个样本
        for i, data_path in enumerate(data_paths):
            sample_name = Path(data_path).stem
            self.sample_names.append(sample_name)
            
            print(f"\n📊 分析样本 {i+1}/{len(data_paths)}: {sample_name}")
            
            # 加载数据
            data = np.load(data_path)
            if data.ndim == 3:
                input_tensor = torch.from_numpy(data[:3]).unsqueeze(0).float()
            else:
                input_tensor = torch.from_numpy(data[:, :3]).float()
            
            input_tensor = input_tensor.to(self.visualizer.device)
            
            # 提取特征
            features = self.visualizer.extract_features_manually(input_tensor)
            self.all_features[sample_name] = features
            
            # 为每个样本创建详细分析
            sample_dir = os.path.join(output_dir, f"sample_{i+1}_{sample_name}")
            self._analyze_single_sample(features, sample_dir, sample_name)
        
        # 跨样本对比分析
        self._cross_sample_analysis(output_dir)
        
        # 生成综合报告
        self._generate_comprehensive_report(output_dir)
    
    def _analyze_single_sample(self, features, sample_dir, sample_name):
        """分析单个样本"""
        os.makedirs(sample_dir, exist_ok=True)
        
        # 1. 生成通道可视化（英文标题）
        self._create_english_channel_view(features, sample_dir, sample_name)
        
        # 2. 生成分支对比（英文标题）
        self._create_english_branch_comparison(features, sample_dir, sample_name)
        
        # 3. 生成详细的单样本分析（中文报告）
        self._create_chinese_sample_analysis(features, sample_dir, sample_name)
    
    def _create_english_channel_view(self, features, sample_dir, sample_name, max_channels=24):
        """创建英文标题的通道可视化"""
        os.makedirs(sample_dir, exist_ok=True)
        
        # 按类型分组特征
        feature_groups = {
            'VAN_Encoder': {k: v for k, v in features.items() if 'van_encode' in k},
            'VAN_Decoder': {k: v for k, v in features.items() if 'van_decode' in k},
            'UNet_Encoder': {k: v for k, v in features.items() if 'unet_encode' in k},
            'UNet_Decoder': {k: v for k, v in features.items() if 'unet_decode' in k}
        }
        
        for group_name, group_features in feature_groups.items():
            if not group_features:
                continue
                
            print(f"\n=== {group_name} ===")
            
            for layer_name, feature in group_features.items():
                if feature.dim() != 4:
                    continue
                    
                feat_np = feature[0].cpu().numpy()  # (C, H, W)
                total_channels = feat_np.shape[0]
                
                print(f"{layer_name}: {feat_np.shape} (showing first {min(total_channels, max_channels)} channels)")
                
                # 限制显示的通道数
                show_channels = min(total_channels, max_channels)
                
                # 计算子图布局
                channels_per_row = 6
                rows = (show_channels + channels_per_row - 1) // channels_per_row
                
                fig, axes = plt.subplots(rows, channels_per_row, 
                                       figsize=(2*channels_per_row, 2*rows))
                
                if rows == 1:
                    axes = axes.reshape(1, -1) if show_channels > 1 else [axes]
                elif show_channels == 1:
                    axes = [[axes]]
                
                for i in range(show_channels):
                    row = i // channels_per_row
                    col = i % channels_per_row
                    
                    if rows == 1:
                        ax = axes[col] if show_channels > 1 else axes
                    else:
                        ax = axes[row, col]
                    
                    im = ax.imshow(feat_np[i], cmap='viridis', aspect='auto')
                    ax.set_title(f'Ch{i}', fontsize=8)  # 英文标题
                    ax.set_xticks([])
                    ax.set_yticks([])
                    
                    # 添加小的颜色条
                    plt.colorbar(im, ax=ax, shrink=0.8, pad=0.02)
                
                # 隐藏多余的子图
                for i in range(show_channels, rows * channels_per_row):
                    row = i // channels_per_row
                    col = i % channels_per_row
                    if rows == 1:
                        if show_channels > 1:
                            axes[col].axis('off')
                    else:
                        axes[row, col].axis('off')
                
                # 英文标题
                plt.suptitle(f'{group_name} - {layer_name}\nTotal Channels: {total_channels}, Showing: {show_channels}', 
                           fontsize=12)
                plt.tight_layout()
                
                # 保存
                safe_name = layer_name.replace('/', '_').replace('\\', '_')
                save_path = os.path.join(sample_dir, f'{sample_name}_{safe_name}_channels.png')
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                plt.close()
                
                print(f"  保存: {save_path}")
    
    def _create_english_branch_comparison(self, features, sample_dir, sample_name):
        """创建英文标题的分支对比图"""
        os.makedirs(sample_dir, exist_ok=True)
        
        # 提取对应层进行对比
        comparison_pairs = [
            ('van_decode_up4', 'unet_decode_up1', 'Deepest Decoder Layer'),
            ('van_decode_up3', 'unet_decode_up2', 'Deep Decoder Layer'),
            ('van_decode_up2', 'unet_decode_up3', 'Shallow Decoder Layer'),
            ('van_decode_final', 'unet_decode_final', 'Final Output Layer')
        ]
        
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        
        for i, (van_layer, unet_layer, layer_desc) in enumerate(comparison_pairs):
            if van_layer in features and unet_layer in features:
                van_feat = features[van_layer][0].cpu().numpy()
                unet_feat = features[unet_layer][0].cpu().numpy()
                
                # 计算平均激活图
                van_avg = np.mean(van_feat, axis=0)
                unet_avg = np.mean(unet_feat, axis=0)
                
                # 显示VAN分支
                im1 = axes[0, i].imshow(van_avg, cmap='hot', aspect='auto')
                axes[0, i].set_title(f'VAN Branch\n{layer_desc}')  # 英文标题
                axes[0, i].set_xticks([])
                axes[0, i].set_yticks([])
                plt.colorbar(im1, ax=axes[0, i], shrink=0.8)
                
                # 显示U-Net分支
                im2 = axes[1, i].imshow(unet_avg, cmap='hot', aspect='auto')
                axes[1, i].set_title(f'U-Net Branch\n{layer_desc}')  # 英文标题
                axes[1, i].set_xticks([])
                axes[1, i].set_yticks([])
                plt.colorbar(im2, ax=axes[1, i], shrink=0.8)
                
                # 计算相似度
                van_flat = van_avg.flatten()
                unet_flat = unet_avg.flatten()
                similarity = np.corrcoef(van_flat, unet_flat)[0, 1]
                
                # 在图上添加相似度信息（英文）
                axes[0, i].text(0.02, 0.98, f'Similarity: {similarity:.3f}', 
                              transform=axes[0, i].transAxes, 
                              bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                              verticalalignment='top', fontsize=8)
        
        plt.suptitle(f'VCBNet Dual-Branch Comparison - {sample_name}\n(Average Activation Patterns)', fontsize=16)  # 英文标题
        plt.tight_layout()
        
        save_path = os.path.join(sample_dir, f'{sample_name}_branch_comparison_summary.png')
        plt.savefig(save_path, dpi=200, bbox_inches='tight')
        plt.close()
        
        print(f"保存分支对比总结: {save_path}")
    
    def _create_chinese_sample_analysis(self, features, sample_dir, sample_name):
        """创建中文的详细单样本分析"""
        
        # 计算各种统计指标
        van_stats = self._calculate_branch_stats(features, 'van')
        unet_stats = self._calculate_branch_stats(features, 'unet')
        
        # 计算层间相似度
        similarities = self._calculate_layer_similarities(features)
        
        # 分析空间激活模式
        spatial_analysis = self._analyze_spatial_patterns(features)
        
        # 生成中文详细报告
        report = f"""# 🔬 详细样本分析报告 - {sample_name}

## 📊 特征统计概览

### VAN分支统计
| 层名称 | 通道数 | 平均激活 | 标准差 | 最大值 | 激活率(>0.1) |
|--------|--------|----------|--------|--------|-------------|
"""
        
        for layer_name, stats in van_stats.items():
            report += f"| {layer_name} | {stats['channels']} | {stats['mean']:.4f} | {stats['std']:.4f} | {stats['max']:.4f} | {stats['activation_rate']:.2%} |\n"
        
        report += f"""
### U-Net分支统计
| 层名称 | 通道数 | 平均激活 | 标准差 | 最大值 | 激活率(>0.1) |
|--------|--------|----------|--------|--------|-------------|
"""
        
        for layer_name, stats in unet_stats.items():
            report += f"| {layer_name} | {stats['channels']} | {stats['mean']:.4f} | {stats['std']:.4f} | {stats['max']:.4f} | {stats['activation_rate']:.2%} |\n"
        
        report += f"""

## 🎯 层间相似度分析

### 对应层相似度
| VAN层 | U-Net层 | 余弦相似度 | 皮尔逊相关 | 解释 |
|-------|---------|------------|------------|------|
"""
        
        for van_layer, unet_layer, cosine_sim, pearson_corr in similarities:
            interpretation = self._interpret_similarity(cosine_sim, pearson_corr)
            report += f"| {van_layer} | {unet_layer} | {cosine_sim:.4f} | {pearson_corr:.4f} | {interpretation} |\n"
        
        report += f"""

## 🗺️ 空间激活模式分析

### 激活热点分布
"""
        
        for layer_name, spatial_info in spatial_analysis.items():
            report += f"- **{layer_name}**: 主要激活区域在{spatial_info['hotspot_location']}，覆盖{spatial_info['coverage']:.1%}的空间\n"
        
        report += f"""

## 💡 单样本改进建议

### 基于激活强度的建议
"""
        
        # 找出最强和最弱的层
        all_activations = {}
        for branch_stats in [van_stats, unet_stats]:
            for layer, stats in branch_stats.items():
                all_activations[layer] = stats['mean']
        
        strongest_layer = max(all_activations, key=all_activations.get)
        weakest_layer = min(all_activations, key=all_activations.get)
        
        report += f"""
1. **最强激活层**: {strongest_layer} (激活值: {all_activations[strongest_layer]:.4f})
   - 这是模型最重要的特征层
   - 建议: 在融合时给予更高权重
   
2. **最弱激活层**: {weakest_layer} (激活值: {all_activations[weakest_layer]:.4f})
   - 这层可能训练不充分或对当前样本不重要
   - 建议: 检查训练策略或考虑结构调整

### 基于相似度的建议
"""
        
        avg_similarity = np.mean([sim[2] for sim in similarities])
        if avg_similarity > 0.7:
            report += "- **高相似度警告**: 两分支特征过于相似，存在冗余\n- 建议: 添加多样性正则化或考虑参数共享\n"
        elif avg_similarity < 0.3:
            report += "- **强互补性**: 两分支学到了很不同的特征\n- 建议: 优化融合策略，考虑自适应权重\n"
        else:
            report += "- **适度互补**: 两分支既有共同点又有差异\n- 建议: 当前融合策略基本合理，可微调权重\n"
        
        # 保存报告
        report_path = os.path.join(sample_dir, f"{sample_name}_detailed_analysis.md")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"  保存详细分析: {report_path}")
    
    def _calculate_branch_stats(self, features, branch_prefix):
        """计算分支统计信息"""
        stats = {}
        
        for layer_name, feature in features.items():
            if branch_prefix in layer_name and feature.dim() == 4:
                feat_np = feature[0].cpu().numpy()
                
                stats[layer_name] = {
                    'channels': feat_np.shape[0],
                    'mean': np.mean(feat_np),
                    'std': np.std(feat_np),
                    'max': np.max(feat_np),
                    'min': np.min(feat_np),
                    'activation_rate': np.mean(feat_np > 0.1)  # 激活率
                }
        
        return stats
    
    def _calculate_layer_similarities(self, features):
        """计算层间相似度"""
        similarities = []
        
        layer_pairs = [
            ('van_decode_up4', 'unet_decode_up1'),
            ('van_decode_up3', 'unet_decode_up2'),
            ('van_decode_up2', 'unet_decode_up3'),
            ('van_decode_final', 'unet_decode_final')
        ]
        
        for van_layer, unet_layer in layer_pairs:
            if van_layer in features and unet_layer in features:
                van_feat = features[van_layer][0].cpu().numpy()
                unet_feat = features[unet_layer][0].cpu().numpy()
                
                # 计算平均激活图
                van_avg = np.mean(van_feat, axis=0)
                unet_avg = np.mean(unet_feat, axis=0)
                
                # 展平
                van_flat = van_avg.flatten()
                unet_flat = unet_avg.flatten()
                
                # 余弦相似度
                cosine_sim = np.dot(van_flat, unet_flat) / (np.linalg.norm(van_flat) * np.linalg.norm(unet_flat))
                
                # 皮尔逊相关系数
                pearson_corr = np.corrcoef(van_flat, unet_flat)[0, 1]
                
                similarities.append((van_layer, unet_layer, cosine_sim, pearson_corr))
        
        return similarities
    
    def _analyze_spatial_patterns(self, features):
        """分析空间激活模式"""
        spatial_analysis = {}
        
        for layer_name, feature in features.items():
            if feature.dim() == 4:
                feat_np = feature[0].cpu().numpy()
                avg_activation = np.mean(feat_np, axis=0)
                
                # 找到激活热点
                threshold = np.percentile(avg_activation, 80)
                hotspots = avg_activation > threshold
                
                # 计算热点位置
                hotspot_indices = np.where(hotspots)
                if len(hotspot_indices[0]) > 0:
                    center_y = np.mean(hotspot_indices[0])
                    center_x = np.mean(hotspot_indices[1])
                    
                    # 确定位置描述
                    h, w = avg_activation.shape
                    if center_y < h/3:
                        y_desc = "上部"
                    elif center_y > 2*h/3:
                        y_desc = "下部"
                    else:
                        y_desc = "中部"
                    
                    if center_x < w/3:
                        x_desc = "左侧"
                    elif center_x > 2*w/3:
                        x_desc = "右侧"
                    else:
                        x_desc = "中央"
                    
                    location = f"{y_desc}{x_desc}"
                else:
                    location = "分散"
                
                spatial_analysis[layer_name] = {
                    'hotspot_location': location,
                    'coverage': np.mean(hotspots)
                }
        
        return spatial_analysis
    
    def _interpret_similarity(self, cosine_sim, pearson_corr):
        """解释相似度数值"""
        if cosine_sim > 0.8:
            return "高度相似，可能冗余"
        elif cosine_sim > 0.5:
            return "中等相似，有一定互补"
        elif cosine_sim > 0.2:
            return "低相似，强互补性"
        else:
            return "极低相似，完全不同"
    
    def _cross_sample_analysis(self, output_dir):
        """跨样本对比分析"""
        print("\n📈 进行跨样本对比分析...")
        
        # 收集所有样本的统计信息
        all_sample_stats = {}
        
        for sample_name, features in self.all_features.items():
            van_stats = self._calculate_branch_stats(features, 'van')
            unet_stats = self._calculate_branch_stats(features, 'unet')
            similarities = self._calculate_layer_similarities(features)
            
            all_sample_stats[sample_name] = {
                'van_stats': van_stats,
                'unet_stats': unet_stats,
                'similarities': similarities
            }
        
        # 生成跨样本对比图（英文标题）
        self._create_english_cross_sample_plots(all_sample_stats, output_dir)

    def _create_english_cross_sample_plots(self, all_sample_stats, output_dir):
        """创建英文标题的跨样本对比图"""

        # 1. 激活强度对比
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # VAN分支激活强度
        van_data = []
        unet_data = []

        for sample_name, stats in all_sample_stats.items():
            for layer_name, layer_stats in stats['van_stats'].items():
                van_data.append([sample_name, layer_name, layer_stats['mean']])

            for layer_name, layer_stats in stats['unet_stats'].items():
                unet_data.append([sample_name, layer_name, layer_stats['mean']])

        # 绘制热力图
        self._plot_english_activation_heatmap(van_data, axes[0, 0], "VAN Branch Activation Intensity")
        self._plot_english_activation_heatmap(unet_data, axes[0, 1], "U-Net Branch Activation Intensity")

        # 2. 相似度趋势
        similarity_trends = {}
        for sample_name, stats in all_sample_stats.items():
            similarities = [sim[2] for sim in stats['similarities']]
            similarity_trends[sample_name] = similarities

        layer_labels = ['Layer1', 'Layer2', 'Layer3', 'Layer4']
        for sample_name, similarities in similarity_trends.items():
            axes[1, 0].plot(layer_labels, similarities, 'o-', label=sample_name, linewidth=2)

        axes[1, 0].set_title('Cross-Sample Similarity Trends')  # 英文标题
        axes[1, 0].set_ylabel('Cosine Similarity')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 3. 分支平衡性分析
        branch_balance = {}
        for sample_name, stats in all_sample_stats.items():
            van_avg = np.mean([s['mean'] for s in stats['van_stats'].values()])
            unet_avg = np.mean([s['mean'] for s in stats['unet_stats'].values()])
            branch_balance[sample_name] = {'VAN': van_avg, 'U-Net': unet_avg}

        samples = list(branch_balance.keys())
        van_values = [branch_balance[s]['VAN'] for s in samples]
        unet_values = [branch_balance[s]['U-Net'] for s in samples]

        x = np.arange(len(samples))
        width = 0.35

        axes[1, 1].bar(x - width/2, van_values, width, label='VAN', alpha=0.8)
        axes[1, 1].bar(x + width/2, unet_values, width, label='U-Net', alpha=0.8)
        axes[1, 1].set_title('Branch Balance Comparison')  # 英文标题
        axes[1, 1].set_ylabel('Average Activation Intensity')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels(samples, rotation=45)
        axes[1, 1].legend()

        plt.tight_layout()
        save_path = os.path.join(output_dir, 'cross_sample_analysis.png')
        plt.savefig(save_path, dpi=200, bbox_inches='tight')
        plt.close()

        print(f"保存跨样本分析图: {save_path}")

    def _plot_english_activation_heatmap(self, data, ax, title):
        """绘制英文标题的激活强度热力图"""
        if not data:
            return

        # 转换数据格式
        samples = list(set([d[0] for d in data]))
        layers = list(set([d[1] for d in data]))

        matrix = np.zeros((len(layers), len(samples)))

        for i, layer in enumerate(layers):
            for j, sample in enumerate(samples):
                for d in data:
                    if d[0] == sample and d[1] == layer:
                        matrix[i, j] = d[2]
                        break

        im = ax.imshow(matrix, cmap='viridis', aspect='auto')
        ax.set_xticks(range(len(samples)))
        ax.set_xticklabels(samples, rotation=45)
        ax.set_yticks(range(len(layers)))
        ax.set_yticklabels(layers)
        ax.set_title(title)  # 英文标题

        # 添加数值标签
        for i in range(len(layers)):
            for j in range(len(samples)):
                ax.text(j, i, f'{matrix[i, j]:.3f}', ha='center', va='center',
                       color='white' if matrix[i, j] > np.mean(matrix) else 'black',
                       fontsize=8)

        plt.colorbar(im, ax=ax, shrink=0.8)

    def _generate_comprehensive_report(self, output_dir):
        """生成中文综合分析报告"""
        print("\n📝 生成综合分析报告...")

        # 收集所有统计信息
        all_stats = {}
        all_similarities = []

        for sample_name, features in self.all_features.items():
            van_stats = self._calculate_branch_stats(features, 'van')
            unet_stats = self._calculate_branch_stats(features, 'unet')
            similarities = self._calculate_layer_similarities(features)

            all_stats[sample_name] = {
                'van_stats': van_stats,
                'unet_stats': unet_stats,
                'similarities': similarities
            }
            all_similarities.extend([sim[2] for sim in similarities])

        # 计算总体统计
        avg_similarity = np.mean(all_similarities)
        std_similarity = np.std(all_similarities)

        # 计算分支平衡性
        van_activations = []
        unet_activations = []

        for stats in all_stats.values():
            van_activations.extend([s['mean'] for s in stats['van_stats'].values()])
            unet_activations.extend([s['mean'] for s in stats['unet_stats'].values()])

        van_avg = np.mean(van_activations)
        unet_avg = np.mean(unet_activations)
        balance_ratio = van_avg / unet_avg if unet_avg > 0 else 0

        # 生成中文报告
        report = f"""# 🔬 VCBNet双分支综合分析报告

## 📋 分析概览
- **分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **样本数量**: {len(self.sample_names)}
- **模型**: VCBNet双分支结构
- **分析样本**: {', '.join(self.sample_names)}

## 🎯 关键发现

### 1. 分支平衡性分析
- **VAN分支平均激活**: {van_avg:.4f}
- **U-Net分支平均激活**: {unet_avg:.4f}
- **平衡比率**: {balance_ratio:.4f} (VAN/U-Net)

**结论**: {'U-Net分支占主导地位' if balance_ratio < 0.5 else 'VAN分支占主导地位' if balance_ratio > 2 else '两分支相对平衡'}

### 2. 特征相似度分析
- **平均相似度**: {avg_similarity:.4f} ± {std_similarity:.4f}
- **相似度范围**: {min(all_similarities):.4f} - {max(all_similarities):.4f}

**解释**: {'高相似度，存在特征冗余' if avg_similarity > 0.7 else '低相似度，强互补性' if avg_similarity < 0.3 else '中等相似度，适度互补'}

## 📊 详细分析结果

### 各样本特征对比
| 样本 | VAN平均激活 | U-Net平均激活 | 平均相似度 | 主导分支 |
|------|-------------|---------------|------------|----------|
"""

        for sample_name, stats in all_stats.items():
            van_avg_sample = np.mean([s['mean'] for s in stats['van_stats'].values()])
            unet_avg_sample = np.mean([s['mean'] for s in stats['unet_stats'].values()])
            sample_similarity = np.mean([sim[2] for sim in stats['similarities']])
            dominant = 'U-Net' if unet_avg_sample > van_avg_sample else 'VAN'

            report += f"| {sample_name} | {van_avg_sample:.4f} | {unet_avg_sample:.4f} | {sample_similarity:.4f} | {dominant} |\n"

        report += f"""

### 层级分析
#### 最强激活层 (跨所有样本)
"""

        # 找出最强的层
        all_layer_activations = {}
        for stats in all_stats.values():
            for branch_stats in [stats['van_stats'], stats['unet_stats']]:
                for layer_name, layer_stats in branch_stats.items():
                    if layer_name not in all_layer_activations:
                        all_layer_activations[layer_name] = []
                    all_layer_activations[layer_name].append(layer_stats['mean'])

        # 计算平均激活
        avg_layer_activations = {k: np.mean(v) for k, v in all_layer_activations.items()}
        sorted_layers = sorted(avg_layer_activations.items(), key=lambda x: x[1], reverse=True)

        for i, (layer_name, avg_activation) in enumerate(sorted_layers[:5]):
            report += f"{i+1}. **{layer_name}**: {avg_activation:.4f}\n"

        report += f"""

## 🔧 详细改进建议

### 1. 分支平衡优化
"""

        if balance_ratio < 0.3:
            report += """
**问题**: VAN分支严重弱于U-Net分支
**建议**:
- 增加VAN分支的学习率
- 调整损失函数权重，给VAN分支更多关注
- 检查VAN backbone的预训练权重是否正确加载
- 考虑VAN分支的特征归一化
"""
        elif balance_ratio > 3:
            report += """
**问题**: VAN分支过强，U-Net分支贡献不足
**建议**:
- 增加U-Net分支的复杂度
- 调整融合权重，给U-Net更高权重
- 检查U-Net分支的训练是否充分
"""
        else:
            report += """
**状态**: 分支平衡性良好
**建议**:
- 保持当前的训练策略
- 可以尝试微调融合权重以进一步优化
"""

        report += f"""

### 2. 融合策略优化
"""

        if avg_similarity > 0.7:
            report += """
**问题**: 特征相似度过高，存在冗余
**建议**:
- 实现自适应融合权重: `weight = 1 / (1 + similarity)`
- 添加多样性正则化损失
- 考虑参数共享以减少冗余
- 引入对抗训练促进特征多样性
"""
        elif avg_similarity < 0.2:
            report += """
**优势**: 特征高度互补，但融合可能不充分
**建议**:
- 实现交叉注意力机制
- 使用门控融合: `out = gate * van_out + (1-gate) * unet_out`
- 添加特征对齐模块
- 考虑多尺度融合策略
"""
        else:
            report += """
**状态**: 特征互补性适中
**建议**:
- 当前融合策略基本合理
- 可以尝试学习性融合权重
- 考虑添加注意力机制进一步优化
"""

        # 添加关键问题识别
        critical_issues = []

        if balance_ratio < 0.3:
            critical_issues.append("**严重分支不平衡**: VAN分支严重未充分利用")

        if avg_similarity > 0.8:
            critical_issues.append("**高特征冗余**: 两分支学习相似特征")

        if std_similarity > 0.2:
            critical_issues.append("**高相似度方差**: 跨样本特征模式不一致")

        report += f"""

## 📋 基于当前分析的具体建议

### 发现的关键问题:
"""

        if critical_issues:
            for issue in critical_issues:
                report += f"- {issue}\n"
        else:
            report += "- 未发现关键问题，模型架构基本合理\n"

        report += f"""

### 优先行动:
1. **立即**: {'平衡分支训练' if balance_ratio < 0.5 else '优化融合策略'}
2. **接下来**: {'减少特征冗余' if avg_similarity > 0.7 else '增强特征互补性'}
3. **未来**: 实现高级融合机制

## 📈 图像分析解读

### 跨样本分析图 (cross_sample_analysis.png)
1. **左上角 - VAN分支激活热力图**:
   - 颜色越亮表示激活越强
   - 可以看出哪些层在哪些样本上表现最好

2. **右上角 - U-Net分支激活热力图**:
   - 对比VAN分支，观察两分支的激活模式差异

3. **左下角 - 相似度趋势**:
   - 显示各层相似度在不同样本间的变化
   - 平稳的线表示稳定性好，波动大表示样本敏感性高

4. **右下角 - 分支平衡性**:
   - 直观显示两分支的贡献对比
   - 理想情况是两个柱子高度相近

### 各样本详细分析图
每个样本目录包含:
- **通道图**: 显示各层的特征模式，黄色区域是重要特征
- **分支对比图**: 显示对应层的空间激活模式和相似度

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*分析工具版本: 中文报告英文绘图版v1.0*
"""

        # 保存报告
        report_path = os.path.join(output_dir, 'comprehensive_report.md')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"保存综合报告: {report_path}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='VCBNet综合特征分析 - 中文报告英文绘图版')
    parser.add_argument('--model_path', type=str, default='721/vcbnet0/checkpoints/checkpoint.pth')
    parser.add_argument('--data_dir', type=str, default='data/test')
    parser.add_argument('--output_dir', type=str, default='chinese_report_analysis')
    parser.add_argument('--num_samples', type=int, default=3, help='分析的样本数量')
    
    args = parser.parse_args()
    
    # 获取测试文件
    test_files = list(Path(args.data_dir).glob('*_match.npy'))[:args.num_samples]
    
    if len(test_files) < args.num_samples:
        print(f"警告: 只找到 {len(test_files)} 个文件，少于请求的 {args.num_samples} 个")
    
    print(f"🎯 开始综合分析 {len(test_files)} 个样本...")
    for i, file in enumerate(test_files):
        print(f"  样本 {i+1}: {file.name}")
    
    # 创建分析器
    analyzer = ChineseReportEnglishPlotAnalyzer(args.model_path)
    
    # 执行分析
    analyzer.analyze_multiple_samples([str(f) for f in test_files], args.output_dir)
    
    print(f"\n🎉 综合分析完成！结果保存在: {args.output_dir}")

if __name__ == "__main__":
    main()
