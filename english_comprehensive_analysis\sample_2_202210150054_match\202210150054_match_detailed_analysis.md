# 🔬 Detailed Sample Analysis Report - 202210150054_match

## 📊 Feature Statistics Overview

### VAN Branch Statistics
| Layer Name | Channels | Mean Activation | Std Dev | Max Value | Activation Rate(>0.1) |
|------------|----------|-----------------|---------|-----------|----------------------|
| van_encode_stage1 | 64 | 0.0297 | 0.8134 | 5.5267 | 45.29% |
| van_encode_stage2 | 128 | 0.0016 | 0.8056 | 4.8407 | 41.14% |
| van_encode_stage3 | 320 | 0.0286 | 0.5431 | 2.8441 | 45.21% |
| van_encode_stage4 | 512 | 0.0110 | 0.6427 | 11.5733 | 42.93% |
| van_decode_up4 | 256 | 0.0529 | 1.9576 | 18.8860 | 47.29% |
| van_decode_up3 | 128 | -0.0072 | 1.5099 | 11.3319 | 45.78% |
| van_decode_up2 | 64 | -0.0027 | 1.0334 | 12.6751 | 50.20% |
| van_decode_final | 32 | 0.1024 | 0.3139 | 4.9601 | 35.11% |

### U-Net Branch Statistics
| Layer Name | Channels | Mean Activation | Std Dev | Max Value | Activation Rate(>0.1) |
|------------|----------|-----------------|---------|-----------|----------------------|
| unet_encode_conv | 32 | 0.3075 | 0.4482 | 6.7925 | 51.19% |
| unet_encode_down1 | 64 | 0.2970 | 0.6597 | 18.6282 | 31.19% |
| unet_encode_down2 | 128 | 0.2928 | 0.6486 | 8.4412 | 30.11% |
| unet_encode_down3 | 256 | 0.1794 | 0.5210 | 13.0502 | 19.08% |
| unet_encode_down4 | 512 | 0.1352 | 0.3626 | 8.4713 | 18.99% |
| unet_decode_up1 | 256 | 0.1483 | 0.2517 | 3.3615 | 34.79% |
| unet_decode_up2 | 128 | 0.2159 | 0.3647 | 4.7184 | 36.95% |
| unet_decode_up3 | 64 | 0.2379 | 0.4098 | 12.3607 | 38.57% |
| unet_decode_final | 32 | 0.1765 | 0.3785 | 15.8870 | 32.31% |


## 🎯 Inter-layer Similarity Analysis

### Corresponding Layer Similarities
| VAN Layer | U-Net Layer | Cosine Similarity | Pearson Correlation | Interpretation |
|-----------|-------------|-------------------|---------------------|----------------|
| van_decode_up4 | unet_decode_up1 | 0.4904 | 0.1045 | Low similarity, strong complementarity |
| van_decode_up3 | unet_decode_up2 | -0.1766 | -0.2720 | Very low similarity, completely different |
| van_decode_up2 | unet_decode_up3 | 0.1079 | 0.3712 | Very low similarity, completely different |
| van_decode_final | unet_decode_final | 0.7890 | 0.4467 | Moderately similar, some complementarity |


## 🗺️ Spatial Activation Pattern Analysis

### Activation Hotspot Distribution
- **van_encode_stage1**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_encode_stage2**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_encode_stage3**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_encode_stage4**: Main activation regions at middle-center, covering 20.1% of spatial area
- **van_decode_up4**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_decode_up3**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_decode_up2**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_decode_final**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_encode_conv**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_encode_down1**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_encode_down2**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_encode_down3**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_encode_down4**: Main activation regions at middle-center, covering 20.1% of spatial area
- **unet_decode_up1**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_decode_up2**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_decode_up3**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_decode_final**: Main activation regions at middle-center, covering 20.0% of spatial area


## 💡 Single Sample Improvement Suggestions

### Based on Activation Intensity

1. **Strongest Activation Layer**: unet_encode_conv (activation: 0.3075)
   - This is the most important feature layer of the model
   - Suggestion: Give higher weight in fusion
   
2. **Weakest Activation Layer**: van_decode_up3 (activation: -0.0072)
   - This layer may be under-trained or unimportant for current sample
   - Suggestion: Check training strategy or consider structural adjustment

### Based on Similarity Analysis
- **Moderate Complementarity**: Two branches have both commonalities and differences
- Suggestion: Current fusion strategy is basically reasonable, can fine-tune weights
