# test.py

import torch
import sys
from model import VCBNet3_WT

def test_vunet():
    """
    测试VUNet模型的功能、输出形状和参数数量。
    """
    # ---- 1. 模型初始化 ----
    # n_channels=3: 输入图像是RGB三通道
    # n_outputs=1:  输出是单通道（例如，回归预测图）
    # pretrained_backbone=True: 尝试加载预训练的VAN骨干权重
    print("Initializing VUNet model...")
    try:
        model = VCBNet3_WT(n_channels=3, n_outputs=1)
        model.eval()  # 设置为评估模式
    except Exception as e:
        print(f"Error initializing model: {e}")
        return

    test_input = torch.randn(1, 3, 768, 1536)
    
    print(f"输入张量形状: {test_input.shape}")
    
    # 前向传播测试
    with torch.no_grad():
       out = model(test_input)
    print(f"输出张量形状: {out.shape}")
        
    # 计算模型参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")


if __name__ == '__main__':
    test_vunet()