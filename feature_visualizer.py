#!/usr/bin/env python3
"""
VCBNet双分支特征可视化工具
用于分析VAN分支和U-Net分支在解码过程中的特征差异
"""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import torch.nn.functional as F
from model import VCBNet
import json

class VCBNetFeatureVisualizer:
    def __init__(self, model_path, device='auto'):
        """
        初始化特征可视化器
        
        Args:
            model_path: 模型checkpoint路径
            device: 计算设备
        """
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        self.model = self._load_model(model_path)
        self.features = {}  # 存储中间特征
        self._register_hooks()
        
    def _load_model(self, model_path):
        """加载模型"""
        # 加载配置
        model_dir = os.path.dirname(os.path.dirname(model_path))  # 从checkpoints目录回到模型目录
        config_path = os.path.join(model_dir, 'model_param.json')
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # 创建模型
        model = VCBNet(n_channels=config['in_shape'][0], n_outputs=config['out_chans'])
        
        # 加载权重
        checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
        state_dict = checkpoint['model']
        
        # 处理accelerator前缀
        new_state_dict = {}
        for key, value in state_dict.items():
            if key.startswith('_orig_mod.'):
                new_key = key[10:]
            else:
                new_key = key
            new_state_dict[new_key] = value
        
        model.load_state_dict(new_state_dict, strict=False)
        model.to(self.device)
        model.eval()
        
        return model
    
    def _register_hooks(self):
        """注册前向钩子以捕获中间特征"""
        
        def get_activation(name):
            def hook(model, input, output):
                self.features[name] = output.detach()
            return hook
        
        # VAN分支的关键层
        self.model.up4.register_forward_hook(get_activation('van_up4'))
        self.model.up3.register_forward_hook(get_activation('van_up3'))
        self.model.up2.register_forward_hook(get_activation('van_up2'))
        self.model.up1.register_forward_hook(get_activation('van_up1'))
        
        # U-Net分支的关键层
        self.model.up1_1.register_forward_hook(get_activation('unet_up1'))
        self.model.up1_2.register_forward_hook(get_activation('unet_up2'))
        self.model.up1_3.register_forward_hook(get_activation('unet_up3'))
        self.model.up1_4.register_forward_hook(get_activation('unet_up4'))
        
        # 编码器特征
        self.model.down1_1.register_forward_hook(get_activation('unet_down1'))
        self.model.down1_2.register_forward_hook(get_activation('unet_down2'))
        self.model.down1_3.register_forward_hook(get_activation('unet_down3'))
        self.model.down1_4.register_forward_hook(get_activation('unet_down4'))
    
    def extract_features(self, input_tensor):
        """提取特征"""
        self.features.clear()
        with torch.no_grad():
            _ = self.model(input_tensor)
        return self.features
    
    def visualize_feature_maps(self, features, save_dir, sample_name, max_channels=16):
        """可视化特征图"""
        os.makedirs(save_dir, exist_ok=True)
        
        for layer_name, feature in features.items():
            if feature.dim() != 4:  # 跳过非特征图
                continue
                
            batch_size, channels, height, width = feature.shape
            feature_np = feature[0].cpu().numpy()  # 取第一个样本
            
            # 选择要可视化的通道数
            num_channels = min(channels, max_channels)
            
            # 创建子图
            cols = 4
            rows = (num_channels + cols - 1) // cols
            fig, axes = plt.subplots(rows, cols, figsize=(16, 4*rows))
            if rows == 1:
                axes = axes.reshape(1, -1)
            
            for i in range(num_channels):
                row, col = i // cols, i % cols
                ax = axes[row, col]
                
                # 可视化特征图
                im = ax.imshow(feature_np[i], cmap='viridis', aspect='auto')
                ax.set_title(f'Channel {i}')
                ax.axis('off')
                plt.colorbar(im, ax=ax, shrink=0.8)
            
            # 隐藏多余的子图
            for i in range(num_channels, rows * cols):
                row, col = i // cols, i % cols
                axes[row, col].axis('off')
            
            plt.suptitle(f'{layer_name} - {sample_name}', fontsize=16)
            plt.tight_layout()
            
            save_path = os.path.join(save_dir, f'{sample_name}_{layer_name}_channels.png')
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"保存特征图: {save_path}")
    
    def visualize_feature_statistics(self, features, save_dir, sample_name):
        """可视化特征统计信息"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 1. 特征激活强度对比
        van_layers = ['van_up4', 'van_up3', 'van_up2', 'van_up1']
        unet_layers = ['unet_up1', 'unet_up2', 'unet_up3', 'unet_up4']
        
        van_means = []
        unet_means = []
        van_stds = []
        unet_stds = []
        
        for van_layer, unet_layer in zip(van_layers, unet_layers):
            if van_layer in features:
                van_feat = features[van_layer][0].cpu().numpy()
                van_means.append(np.mean(van_feat))
                van_stds.append(np.std(van_feat))
            
            if unet_layer in features:
                unet_feat = features[unet_layer][0].cpu().numpy()
                unet_means.append(np.mean(unet_feat))
                unet_stds.append(np.std(unet_feat))
        
        # 绘制激活强度对比
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        x = range(len(van_means))
        ax1.plot(x, van_means, 'o-', label='VAN Branch', linewidth=2, markersize=8)
        ax1.plot(x, unet_means, 's-', label='U-Net Branch', linewidth=2, markersize=8)
        ax1.set_xlabel('Decoder Layer')
        ax1.set_ylabel('Mean Activation')
        ax1.set_title('Feature Activation Intensity')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        ax2.plot(x, van_stds, 'o-', label='VAN Branch', linewidth=2, markersize=8)
        ax2.plot(x, unet_stds, 's-', label='U-Net Branch', linewidth=2, markersize=8)
        ax2.set_xlabel('Decoder Layer')
        ax2.set_ylabel('Std Activation')
        ax2.set_title('Feature Activation Diversity')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.suptitle(f'Branch Comparison - {sample_name}', fontsize=16)
        plt.tight_layout()
        
        save_path = os.path.join(save_dir, f'{sample_name}_branch_comparison.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"保存统计图: {save_path}")
    
    def visualize_spatial_attention(self, features, save_dir, sample_name):
        """可视化空间注意力模式"""
        os.makedirs(save_dir, exist_ok=True)
        
        van_layers = ['van_up4', 'van_up3', 'van_up2', 'van_up1']
        unet_layers = ['unet_up1', 'unet_up2', 'unet_up3', 'unet_up4']
        
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        
        for i, (van_layer, unet_layer) in enumerate(zip(van_layers, unet_layers)):
            # VAN分支空间注意力
            if van_layer in features:
                van_feat = features[van_layer][0].cpu().numpy()
                van_attention = np.mean(van_feat, axis=0)  # 通道维度平均
                
                im1 = axes[0, i].imshow(van_attention, cmap='hot', aspect='auto')
                axes[0, i].set_title(f'VAN {van_layer}')
                axes[0, i].axis('off')
                plt.colorbar(im1, ax=axes[0, i], shrink=0.8)
            
            # U-Net分支空间注意力
            if unet_layer in features:
                unet_feat = features[unet_layer][0].cpu().numpy()
                unet_attention = np.mean(unet_feat, axis=0)  # 通道维度平均
                
                im2 = axes[1, i].imshow(unet_attention, cmap='hot', aspect='auto')
                axes[1, i].set_title(f'U-Net {unet_layer}')
                axes[1, i].axis('off')
                plt.colorbar(im2, ax=axes[1, i], shrink=0.8)
        
        plt.suptitle(f'Spatial Attention Patterns - {sample_name}', fontsize=16)
        plt.tight_layout()
        
        save_path = os.path.join(save_dir, f'{sample_name}_spatial_attention.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"保存空间注意力图: {save_path}")

    def analyze_feature_similarity(self, features, save_dir, sample_name):
        """分析两个分支特征的相似性"""
        os.makedirs(save_dir, exist_ok=True)

        van_layers = ['van_up4', 'van_up3', 'van_up2', 'van_up1']
        unet_layers = ['unet_up1', 'unet_up2', 'unet_up3', 'unet_up4']

        similarities = []
        layer_names = []

        for van_layer, unet_layer in zip(van_layers, unet_layers):
            if van_layer in features and unet_layer in features:
                van_feat = features[van_layer][0].cpu().numpy()
                unet_feat = features[unet_layer][0].cpu().numpy()

                # 调整尺寸到相同大小
                if van_feat.shape != unet_feat.shape:
                    min_h = min(van_feat.shape[1], unet_feat.shape[1])
                    min_w = min(van_feat.shape[2], unet_feat.shape[2])
                    van_feat = van_feat[:, :min_h, :min_w]
                    unet_feat = unet_feat[:, :min_h, :min_w]

                # 计算余弦相似度
                van_flat = van_feat.flatten()
                unet_flat = unet_feat.flatten()

                similarity = np.dot(van_flat, unet_flat) / (np.linalg.norm(van_flat) * np.linalg.norm(unet_flat))
                similarities.append(similarity)
                layer_names.append(f'Layer {len(similarities)}')

        # 绘制相似性分析
        plt.figure(figsize=(10, 6))
        bars = plt.bar(layer_names, similarities, color=['skyblue', 'lightcoral', 'lightgreen', 'gold'])
        plt.ylabel('Cosine Similarity')
        plt.title(f'Feature Similarity Between VAN and U-Net Branches - {sample_name}')
        plt.ylim(0, 1)

        # 添加数值标签
        for bar, sim in zip(bars, similarities):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{sim:.3f}', ha='center', va='bottom')

        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        save_path = os.path.join(save_dir, f'{sample_name}_feature_similarity.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()

        print(f"保存相似性分析: {save_path}")
        return similarities

    def generate_comprehensive_report(self, features, save_dir, sample_name):
        """生成综合分析报告"""
        similarities = self.analyze_feature_similarity(features, save_dir, sample_name)

        # 分析报告
        report = f"""
# VCBNet双分支特征分析报告 - {sample_name}

## 模型结构分析
- **VAN分支**: 基于Visual Attention Network的编码器-解码器
- **U-Net分支**: 传统U-Net结构的编码器-解码器
- **融合策略**: 简单平均融合 (0.5 * VAN + 0.5 * U-Net)

## 特征层分析

### 解码器层对应关系
| VAN分支 | U-Net分支 | 特征相似度 |
|---------|-----------|------------|
"""

        van_layers = ['van_up4', 'van_up3', 'van_up2', 'van_up1']
        unet_layers = ['unet_up1', 'unet_up2', 'unet_up3', 'unet_up4']

        for i, (van_layer, unet_layer) in enumerate(zip(van_layers, unet_layers)):
            if i < len(similarities):
                report += f"| {van_layer} | {unet_layer} | {similarities[i]:.3f} |\n"

        # 特征统计
        van_stats = []
        unet_stats = []

        for van_layer, unet_layer in zip(van_layers, unet_layers):
            if van_layer in features:
                van_feat = features[van_layer][0].cpu().numpy()
                van_stats.append({
                    'mean': np.mean(van_feat),
                    'std': np.std(van_feat),
                    'max': np.max(van_feat),
                    'min': np.min(van_feat)
                })

            if unet_layer in features:
                unet_feat = features[unet_layer][0].cpu().numpy()
                unet_stats.append({
                    'mean': np.mean(unet_feat),
                    'std': np.std(unet_feat),
                    'max': np.max(unet_feat),
                    'min': np.min(unet_feat)
                })

        report += f"""

## 改进建议

### 基于相似性分析
- **高相似度层** (>0.8): 考虑共享参数或减少冗余
- **低相似度层** (<0.5): 两分支学到了不同特征，融合策略可优化
- **中等相似度层** (0.5-0.8): 互补特征，当前融合策略合理

### 基于激活统计
- **激活强度差异大**: 考虑自适应权重融合
- **激活分布不同**: 可能需要特征对齐或归一化

### 具体改进方向
1. **自适应融合权重**: 根据特征相似度动态调整融合权重
2. **注意力机制**: 在融合时引入空间和通道注意力
3. **特征对齐**: 在融合前对特征进行对齐处理
4. **层级融合**: 不同层使用不同的融合策略

## 文件输出
- 特征图可视化: `*_channels.png`
- 分支对比分析: `*_branch_comparison.png`
- 空间注意力: `*_spatial_attention.png`
- 相似性分析: `*_feature_similarity.png`
"""

        # 保存报告
        report_path = os.path.join(save_dir, f'{sample_name}_analysis_report.md')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"保存分析报告: {report_path}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='VCBNet特征可视化')
    parser.add_argument('--model_path', type=str, default='721/vcbnet0/checkpoints/checkpoint.pth',
                       help='模型checkpoint路径')
    parser.add_argument('--data_path', type=str, default='data/test/202210090536_match.npy',
                       help='测试数据路径')
    parser.add_argument('--output_dir', type=str, default='feature_analysis',
                       help='输出目录')
    parser.add_argument('--max_channels', type=int, default=16,
                       help='可视化的最大通道数')
    
    args = parser.parse_args()
    
    # 创建可视化器
    visualizer = VCBNetFeatureVisualizer(args.model_path)
    
    # 加载测试数据
    data = np.load(args.data_path)
    if data.ndim == 3:  # (C, H, W)
        input_tensor = torch.from_numpy(data[:3]).unsqueeze(0).float()
    else:  # (N, C, H, W)
        input_tensor = torch.from_numpy(data[:, :3]).float()
    
    input_tensor = input_tensor.to(visualizer.device)
    
    # 提取特征
    print("提取中间特征...")
    features = visualizer.extract_features(input_tensor)
    
    sample_name = Path(args.data_path).stem
    
    # 可视化特征图
    print("生成特征图可视化...")
    visualizer.visualize_feature_maps(features, args.output_dir, sample_name, args.max_channels)
    
    # 可视化统计信息
    print("生成统计分析...")
    visualizer.visualize_feature_statistics(features, args.output_dir, sample_name)
    
    # 可视化空间注意力
    print("生成空间注意力分析...")
    visualizer.visualize_spatial_attention(features, args.output_dir, sample_name)

    # 生成综合分析报告
    print("生成综合分析报告...")
    visualizer.generate_comprehensive_report(features, args.output_dir, sample_name)

    print(f"所有可视化结果保存在: {args.output_dir}")
    print("\n生成的文件:")
    print("- 特征图可视化: *_channels.png")
    print("- 分支对比分析: *_branch_comparison.png")
    print("- 空间注意力: *_spatial_attention.png")
    print("- 相似性分析: *_feature_similarity.png")
    print("- 综合分析报告: *_analysis_report.md")

if __name__ == "__main__":
    main()
