# 🔬 Detailed Sample Analysis Report - 202210090536_match

## 📊 Feature Statistics Overview

### VAN Branch Statistics
| Layer Name | Channels | Mean Activation | Std Dev | Max Value | Activation Rate(>0.1) |
|------------|----------|-----------------|---------|-----------|----------------------|
| van_encode_stage1 | 64 | 0.0378 | 0.7859 | 5.0773 | 45.53% |
| van_encode_stage2 | 128 | 0.0025 | 0.7988 | 4.4574 | 40.39% |
| van_encode_stage3 | 320 | 0.0291 | 0.5474 | 2.8595 | 45.69% |
| van_encode_stage4 | 512 | 0.0123 | 0.6272 | 9.6677 | 42.86% |
| van_decode_up4 | 256 | 0.0502 | 1.9265 | 17.8945 | 47.30% |
| van_decode_up3 | 128 | 0.0010 | 1.5451 | 11.2951 | 45.68% |
| van_decode_up2 | 64 | 0.0764 | 1.0948 | 13.8966 | 53.78% |
| van_decode_final | 32 | 0.1156 | 0.3284 | 4.6900 | 34.57% |

### U-Net Branch Statistics
| Layer Name | Channels | Mean Activation | Std Dev | Max Value | Activation Rate(>0.1) |
|------------|----------|-----------------|---------|-----------|----------------------|
| unet_encode_conv | 32 | 0.5363 | 0.9857 | 8.4647 | 44.37% |
| unet_encode_down1 | 64 | 0.6697 | 1.5456 | 19.8759 | 28.99% |
| unet_encode_down2 | 128 | 0.5462 | 1.4191 | 16.2007 | 26.80% |
| unet_encode_down3 | 256 | 0.1802 | 0.6408 | 20.5644 | 13.61% |
| unet_encode_down4 | 512 | 0.1120 | 0.3416 | 6.6838 | 15.32% |
| unet_decode_up1 | 256 | 0.1527 | 0.2541 | 3.9225 | 36.15% |
| unet_decode_up2 | 128 | 0.2735 | 0.4498 | 5.0611 | 40.17% |
| unet_decode_up3 | 64 | 0.3771 | 0.5568 | 7.4736 | 45.45% |
| unet_decode_final | 32 | 0.1942 | 0.3908 | 7.9982 | 31.27% |


## 🎯 Inter-layer Similarity Analysis

### Corresponding Layer Similarities
| VAN Layer | U-Net Layer | Cosine Similarity | Pearson Correlation | Interpretation |
|-----------|-------------|-------------------|---------------------|----------------|
| van_decode_up4 | unet_decode_up1 | 0.4717 | 0.0636 | Low similarity, strong complementarity |
| van_decode_up3 | unet_decode_up2 | 0.0355 | 0.0832 | Very low similarity, completely different |
| van_decode_up2 | unet_decode_up3 | 0.8538 | 0.6998 | Highly similar, possible redundancy |
| van_decode_final | unet_decode_final | 0.8425 | 0.3567 | Highly similar, possible redundancy |


## 🗺️ Spatial Activation Pattern Analysis

### Activation Hotspot Distribution
- **van_encode_stage1**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_encode_stage2**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_encode_stage3**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_encode_stage4**: Main activation regions at middle-center, covering 20.1% of spatial area
- **van_decode_up4**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_decode_up3**: Main activation regions at middle-center, covering 20.0% of spatial area
- **van_decode_up2**: Main activation regions at lower-center, covering 20.0% of spatial area
- **van_decode_final**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_encode_conv**: Main activation regions at lower-center, covering 20.0% of spatial area
- **unet_encode_down1**: Main activation regions at lower-center, covering 20.0% of spatial area
- **unet_encode_down2**: Main activation regions at lower-center, covering 20.0% of spatial area
- **unet_encode_down3**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_encode_down4**: Main activation regions at middle-center, covering 20.1% of spatial area
- **unet_decode_up1**: Main activation regions at middle-center, covering 20.0% of spatial area
- **unet_decode_up2**: Main activation regions at lower-center, covering 20.0% of spatial area
- **unet_decode_up3**: Main activation regions at lower-center, covering 20.0% of spatial area
- **unet_decode_final**: Main activation regions at middle-center, covering 20.0% of spatial area


## 💡 Single Sample Improvement Suggestions

### Based on Activation Intensity

1. **Strongest Activation Layer**: unet_encode_down1 (activation: 0.6697)
   - This is the most important feature layer of the model
   - Suggestion: Give higher weight in fusion
   
2. **Weakest Activation Layer**: van_decode_up3 (activation: 0.0010)
   - This layer may be under-trained or unimportant for current sample
   - Suggestion: Check training strategy or consider structural adjustment

### Based on Similarity Analysis
- **Moderate Complementarity**: Two branches have both commonalities and differences
- Suggestion: Current fusion strategy is basically reasonable, can fine-tune weights
