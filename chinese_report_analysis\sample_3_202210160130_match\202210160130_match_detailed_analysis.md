# 🔬 详细样本分析报告 - 202210160130_match

## 📊 特征统计概览

### VAN分支统计
| 层名称 | 通道数 | 平均激活 | 标准差 | 最大值 | 激活率(>0.1) |
|--------|--------|----------|--------|--------|-------------|
| van_encode_stage1 | 64 | 0.0291 | 0.8084 | 5.1524 | 44.88% |
| van_encode_stage2 | 128 | 0.0016 | 0.8041 | 4.7986 | 41.39% |
| van_encode_stage3 | 320 | 0.0286 | 0.5482 | 2.9226 | 45.25% |
| van_encode_stage4 | 512 | 0.0096 | 0.6478 | 11.7537 | 42.96% |
| van_decode_up4 | 256 | 0.0412 | 1.9541 | 18.4921 | 47.11% |
| van_decode_up3 | 128 | -0.0007 | 1.5543 | 9.7621 | 45.53% |
| van_decode_up2 | 64 | 0.0050 | 1.0360 | 15.0338 | 50.34% |
| van_decode_final | 32 | 0.1071 | 0.3251 | 4.6028 | 34.55% |

### U-Net分支统计
| 层名称 | 通道数 | 平均激活 | 标准差 | 最大值 | 激活率(>0.1) |
|--------|--------|----------|--------|--------|-------------|
| unet_encode_conv | 32 | 0.3321 | 0.5182 | 7.4908 | 52.03% |
| unet_encode_down1 | 64 | 0.3344 | 0.7742 | 13.6753 | 32.39% |
| unet_encode_down2 | 128 | 0.3117 | 0.7377 | 10.5843 | 30.29% |
| unet_encode_down3 | 256 | 0.1881 | 0.5502 | 13.2479 | 19.62% |
| unet_encode_down4 | 512 | 0.1409 | 0.3705 | 9.0926 | 19.56% |
| unet_decode_up1 | 256 | 0.1518 | 0.2551 | 4.0995 | 35.50% |
| unet_decode_up2 | 128 | 0.2081 | 0.3537 | 4.9480 | 36.69% |
| unet_decode_up3 | 64 | 0.2387 | 0.4111 | 11.4684 | 38.81% |
| unet_decode_final | 32 | 0.1876 | 0.3904 | 13.8063 | 32.92% |


## 🎯 层间相似度分析

### 对应层相似度
| VAN层 | U-Net层 | 余弦相似度 | 皮尔逊相关 | 解释 |
|-------|---------|------------|------------|------|
| van_decode_up4 | unet_decode_up1 | 0.4057 | 0.1168 | 低相似，强互补性 |
| van_decode_up3 | unet_decode_up2 | -0.1008 | -0.2790 | 极低相似，完全不同 |
| van_decode_up2 | unet_decode_up3 | 0.2596 | 0.4600 | 低相似，强互补性 |
| van_decode_final | unet_decode_final | 0.7656 | 0.3414 | 中等相似，有一定互补 |


## 🗺️ 空间激活模式分析

### 激活热点分布
- **van_encode_stage1**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_encode_stage2**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_encode_stage3**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_encode_stage4**: 主要激活区域在中部中央，覆盖20.1%的空间
- **van_decode_up4**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_decode_up3**: 主要激活区域在中部中央，覆盖20.0%的空间
- **van_decode_up2**: 主要激活区域在下部中央，覆盖20.0%的空间
- **van_decode_final**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_encode_conv**: 主要激活区域在下部右侧，覆盖20.0%的空间
- **unet_encode_down1**: 主要激活区域在下部右侧，覆盖20.0%的空间
- **unet_encode_down2**: 主要激活区域在下部右侧，覆盖20.0%的空间
- **unet_encode_down3**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_encode_down4**: 主要激活区域在中部中央，覆盖20.1%的空间
- **unet_decode_up1**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_decode_up2**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_decode_up3**: 主要激活区域在中部中央，覆盖20.0%的空间
- **unet_decode_final**: 主要激活区域在中部中央，覆盖20.0%的空间


## 💡 单样本改进建议

### 基于激活强度的建议

1. **最强激活层**: unet_encode_down1 (激活值: 0.3344)
   - 这是模型最重要的特征层
   - 建议: 在融合时给予更高权重
   
2. **最弱激活层**: van_decode_up3 (激活值: -0.0007)
   - 这层可能训练不充分或对当前样本不重要
   - 建议: 检查训练策略或考虑结构调整

### 基于相似度的建议
- **适度互补**: 两分支既有共同点又有差异
- 建议: 当前融合策略基本合理，可微调权重
