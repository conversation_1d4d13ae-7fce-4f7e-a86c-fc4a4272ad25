# 模型预测性能对比报告

## 测试环境
- **测试数据**: data/test 目录下的11个测试文件
- **数据类型**: vis (可见光数据)
- **输入尺寸**: [3, 416, 448]
- **输出通道**: 1 (降水量预测)

## 模型对比结果

### VCBNet0 (721/vcbnet0)
- **模型类型**: vcbnet (原始VCBNet)
- **参数加载**: 515/515 (100% 完全匹配)
- **平均性能指标**:
  - MSE: **4.41**
  - MAE: **0.74**
  - RMSE: **2.08**
  - Correlation: **0.915**

### VCBNet3 (721/vcbnet3_0)
- **模型类型**: vcbnet3 (带融合模块的改进版)
- **参数加载**: 部分加载 (fusion模块参数不匹配)
- **平均性能指标**:
  - MSE: 10.63
  - MAE: 2.02
  - RMSE: 3.25
  - Correlation: 0.743

## 性能分析

### VCBNet0 优势
1. **更低的预测误差**: MSE比VCBNet3低58.5%
2. **更高的预测精度**: MAE比VCBNet3低63.3%
3. **更强的相关性**: 相关系数高达0.915，表明预测与真实值高度相关
4. **模型稳定性**: 所有参数完全匹配，模型加载完整

### 可能原因分析
1. **VCBNet3参数不匹配**: fusion模块的参数形状不匹配可能影响了性能
2. **模型复杂度**: VCBNet3的融合模块可能需要更多训练才能收敛
3. **训练状态**: VCBNet0可能训练得更充分

## 建议

### 对于生产使用
- **推荐使用VCBNet0**: 性能更稳定，预测精度更高
- **预测质量**: 相关系数0.915表明预测结果非常可靠

### 对于研究改进
- **修复VCBNet3**: 检查fusion模块的参数匹配问题
- **重新训练**: 考虑重新训练VCBNet3以获得更好的性能

## 文件输出

### VCBNet0
- **预测结果**: `predictions_vcbnet0/`
- **可视化图像**: `plots_vcbnet0/`

### VCBNet3
- **预测结果**: `predictions/`
- **可视化图像**: `plots/`

## 使用方法

### 运行VCBNet0预测
```bash
conda activate torch
python predict.py --model_dir 721/vcbnet0 --test_data_dir data/test --output_dir predictions_vcbnet0
python simple_plot.py --save_dir plots_vcbnet0 --pred_dir predictions_vcbnet0 --test_dir data/test --model_name VCBNet0 --calculate_metrics
```

### 运行VCBNet3预测
```bash
conda activate torch
python predict.py --model_dir 721/vcbnet3_0 --test_data_dir data/test --output_dir predictions
python simple_plot.py --save_dir plots --pred_dir predictions --test_dir data/test --model_name VCBNet3 --calculate_metrics
```

---
*报告生成时间: 2025-07-23*
